/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * Generated with the TypeScript template
 * https://github.com/react-native-community/react-native-template-typescript
 *
 * @format
 */

import React, {useEffect, useState, useRef, useCallback} from 'react';
import {useDispatch, useSelector} from 'react-redux';

import {configureDatasources, REGISTER_PLUGINS} from '../web/actions/editorActions';
import useMountEffect from '../web/common/hooks/useMountEffect';
import {initApptileConfig} from './common/apptile/ApptileConfigManager';
import {initPlugins} from './plugins/initPlugins';
import {loadDatasourcePlugins as loadShopifyPlugin} from 'apptile-shopify';
import {loadDatasourcePlugins} from 'apptile-datasource';
import {RegisteredPlugins} from 'apptile-core';
import {LocalStorage as localStorage} from 'apptile-core';
import {AppContainer} from 'apptile-core';
import {DispatchActions} from 'apptile-core';
import {EditorRootState} from '../web/store/EditorRootState';
import {ApptileAnimationsContextProvider, initNavigators} from 'apptile-core';
import AppConfigApi from '../web/api/AppConfigApi';
import {loadWebSDKBundle} from '../web/index.web';
import {makeToast} from '../web/actions/toastActions';

const exponentialBackoffs = [500, 1000, 2000, 3000, 5000, 10000, 10000, 60 * 60 * 1000, 24 * 60 * 60 * 1000];
// const exponentialBackoffs = [1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000];
const App = () => {
  const dispatch = useDispatch();
  const [apptileInit, setApptileInit] = useState(false);

  const appIdSelector = (state: EditorRootState) => state.apptile.appId;
  const appConfigFetchedReduxStateSelector = (state: EditorRootState) => state.appConfig.isFetched;

  const appId = useSelector(appIdSelector);
  const appConfigFetchedReduxState = useSelector(appConfigFetchedReduxStateSelector);
  const nextDelay = useRef(-1);
  const socketConn = useRef<WebSocket | null>(null);
  const connectionTimeout = useRef(null);
  const connectionInProgress = useRef(false);

  const socketUrl = global.PLUGIN_SERVER_URL?.replace('https', 'wss')?.replace('http', 'ws') + '/healthcheck';
  // TODO(gaurav): move this out to a separate file and make a ui widget to show socket status
  const establishWsConn = useCallback(
    withDelay => {
      console.log('WEBSOCKET: connectionInprogress ', connectionInProgress.current);
      if (!connectionInProgress.current) {
        if (!withDelay) {
          socketConn.current = new WebSocket(socketUrl);
          connectionInProgress.current = true;
        } else {
          if (!connectionTimeout.current) {
            const delay = exponentialBackoffs[nextDelay.current];
            nextDelay.current = (nextDelay.current + 1) % exponentialBackoffs.length;
            console.log('WEBSOCKET: Establishing connection with delay: ', withDelay, delay);
            connectionTimeout.current = setTimeout(() => {
              establishWsConn(false);
              connectionTimeout.current = null;
            }, delay);
          }
        }

        const ws: WebSocket | null = socketConn.current;
        if (ws !== null) {
          ws.onopen = () => {
            connectionInProgress.current = false;
            console.log('WEBSOCKET: connected!');
            nextDelay.current = 0;
            ws.send(JSON.stringify({type: 'register', kind: 'browser', appId}));
          };

          ws.onmessage = event => {
            if (event.data instanceof Blob) {
              const reader = new FileReader();
              reader.onload = () => {
                const data = JSON.parse(reader.result);
                console.log('WEBSOCKET: received blob event', data);
                if (data.type === 'compilationDone' && data.appId === appId) {
                  alert('Codepush done!');
                }
              };
              reader.readAsText(event.data);
            } else {
              const data = JSON.parse(event.data);
              console.log('WEBSOCKET: received event', data);
              if (data.type === 'compilationDone' && data.appId === appId) {
                alert('Codepush done!');
              } else if (data.type === 'webSdkCompileDone') {
                loadWebSDKBundle();
                dispatch(makeToast({content: 'New WebSDK bundle loaded', appearances: 'success'}));
              }
            }
          };

          ws.onclose = event => {
            connectionInProgress.current = false;
            console.log('WEBSOCKET: Socket was closed. Will retry with delay.', event);
            establishWsConn(true);
          };

          ws.onerror = error => {
            ws.close();
            console.error('WEBSOCKET: socket errorred out. Will retry with delay', error);
          };
        }
      } else {
        console.error('WEBSOCKET: ignoring connection attempt as one is already in progress');
      }
    },
    [nextDelay, socketConn],
  );

  useEffect(() => {
    if (global.PLUGIN_SERVER_URL) {
      establishWsConn(false);
    }
  }, []);

  useEffect(() => {
    let infraInit = Promise.resolve() as Promise<void | {type: string; payload: any}>;
    if (appId) {
      // TODO(gaurav): Make sure that this manifest call doesn't get made
      // again in AppContainer. Check this on the phone too.
      infraInit = AppConfigApi.fetchAppManifest(appId.toString())
        .then(manifest => {
          return Promise.all([
            initPlugins(manifest),
            initNavigators(manifest),
            loadShopifyPlugin(),
            loadDatasourcePlugins(null),
          ]);
        })
        .then(() => {
          const pluginsLoaded = RegisteredPlugins;
          return dispatch({
            type: REGISTER_PLUGINS,
            payload: pluginsLoaded,
          });
        })
        .catch(err => {
          console.error('Failed to initialize plugins: ', err);
        });
    }

    infraInit.then(() => initApptileConfig()).then(() => setApptileInit(true));

    // return () => {
    //   dispatch({
    //     type: DispatchActions.CLEAN_UP_APP,
    //   });
    // };
  }, [appId, dispatch, setApptileInit]);

  useEffect(() => {
    if (appConfigFetchedReduxState && appId) {
      localStorage.setNamespace(appId as string);
      dispatch(configureDatasources(appId as string));
    }
  }, [appId, appConfigFetchedReduxState, dispatch]);

  return (
    <ApptileAnimationsContextProvider>
      <AppContainer apptileInit={apptileInit} />
    </ApptileAnimationsContextProvider>
  );
};

export default App;
