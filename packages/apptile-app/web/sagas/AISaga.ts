import {SagaIterator} from '@redux-saga/types';
import {all, call, delay, put, select, take, takeEvery, takeLatest} from 'redux-saga/effects';
import {
  // FETCH_CHAT_HISTORY_REQUEST,
  RUN_CHAT_COMPLETION_REQUEST,
  RunChatCompletionRequestPayload,
  SCREEN_CREATION_DONE,
  PLUGIN_CREATION_DONE,
  PLUGIN_ADDED_TO_SCREEN_DONE,
  GLOBAL_STATE_CREATION_DONE,
  runChatCompletionFailure,
  runChatCompletionSuccess,
  // updateLiveMessage,
  // clearLiveMessage,
  screenCreationDone,
  pluginCreationDone,
  pluginAddedToScreenDone,
  globalStateCreationDone,
} from '../actions/aiActions';
import {DispatchAction, DispatchActions} from 'apptile-core';
import {reloadExternalPlugins} from '@/root/app/plugins/initPlugins';
import {APP_SAVE, softRestartConfig} from '../actions/editorActions';
import {store} from 'apptile-core';
import { toolsMap } from './frontendMcpTools';
import { DEFAULT_MODEL, DEFAULT_PROVIDER } from '../components/codeEditor/aiModels';


// StreamProcessor class to handle streaming responses
class StreamProcessor {
  messages: Array<{type: 'text' | 'streamItem'; text: string}> = [];

  private appId: string;
  private partialOffsetForStart = 0;
  private partialOffsetForEnd = 0;
  private status: 'readingStreamItem' | 'readingText';
  private currentText = '';
  private currentPartialItem = '';
  private forPlanner: boolean = true;

  contentBlockStatus: 'waiting' | 'started' | 'ended' | 'compile_error' | 'compile_request' = 'waiting';
  textMessageContent = '';
  private toolCalls: Array<{id: string; tool: string; partialInput: string}> = [];

  constructor(appId: string, forPlanner: boolean) {
    this.status = 'readingText';
    this.appId = appId;
    this.forPlanner = forPlanner;
  }

  read(raw: string, offset: number) {
    const streamItemStart = 'streamItemStart:';

    if (this.status === 'readingText') {
      let j = this.partialOffsetForStart,
        i = offset;
      for (; i < raw.length; ) {
        let streamItemStartFound = false;
        let partialMatch = false;
        let numLookedAhead = 0;
        do {
          if (raw[i] === streamItemStart[j]) {
            partialMatch = true;
            i++;
            j++;
            numLookedAhead++;
            if (j === streamItemStart.length) {
              streamItemStartFound = true;
            }

            if (i === raw.length) {
              this.partialOffsetForStart = j;
            }
          } else {
            j = this.partialOffsetForStart = 0;
            i -= numLookedAhead;
            partialMatch = false;
          }
        } while (partialMatch && !streamItemStartFound && i < raw.length);

        if (streamItemStartFound) {
          this.status = 'readingStreamItem';
          // this.currentText += raw.slice(0, i);
          if (this.currentText) {
            this.messages.push({type: 'text', text: this.currentText});
          }
          this.currentText = '';
          this.readStreamItem(raw, i);
          break;
        } else {
          if (numLookedAhead > 0) {
            this.currentText += raw.slice(i, i + numLookedAhead);
            i += numLookedAhead;
          } else {
            this.currentText += raw[i++];
            if (i >= raw.length) {
              if (this.currentText) {
                this.messages.push({type: 'text', text: this.currentText});
              }
              this.currentText = '';
            }
          }
        }
      }
    } else if (this.status === 'readingStreamItem') {
      this.readStreamItem(raw, 0);
    } else {
      throw new Error('Invalid status: ' + this.status);
    }
  }

  private readStreamItem(raw: string, offset: number) {
    this.partialOffsetForStart = 0;
    const streamItemEnd = ':streamItemEnd';

    let i = offset;
    let j = this.partialOffsetForEnd;
    for (; i < raw.length; ) {
      let streamItemEndFound = false;
      let partialMatchFound = false;
      let numLookAhead = 0;
      do {
        if (raw[i] === streamItemEnd[j]) {
          partialMatchFound = true;
          i++;
          j++;
          numLookAhead++;
          if (j === streamItemEnd.length) {
            streamItemEndFound = true;
          }

          if (i === raw.length) {
            this.partialOffsetForEnd = j;
          }
        } else {
          j = this.partialOffsetForEnd = 0;
          i -= numLookAhead;
          partialMatchFound = false;
        }
      } while (partialMatchFound && !streamItemEndFound && i < raw.length);

      if (streamItemEndFound) {
        this.status = 'readingText';
        // this.currentPartialItem += raw.slice(offset, i);
        this.messages.push({type: 'streamItem', text: this.currentPartialItem});
        this.currentPartialItem = '';
        this.partialOffsetForEnd = 0;

        this.read(raw, i);
        break;
      } else {
        if (numLookAhead > 0) {
          this.currentPartialItem += raw.slice(i, i + numLookAhead);
          i += numLookAhead;
        } else {
          this.currentPartialItem += raw[i++];
          // if (i >= raw.length) {
          //   this.messages.push(this.currentPartialItem);
          //   this.currentPartialItem = "";
          // }
        }
      }
    }
  }

  processStreamItem(
    raw: string,
    liveMessageSubscriber: (
      contentHtml: string, 
      toolcall: Array<{id: string; tool: string; partialInput: string;}>, 
      extra: {isMessageEnd: boolean; isToolEnd: boolean; forPlanner: boolean;}
    ) => void,
  ) {
    try {
      const parsedItem = JSON.parse(raw);
      switch (parsedItem.type) {
        case 'message_start':
          {
            console.log('[AGENT] Clearing tool calls');
            this.toolCalls.length = 0;
            console.log('message_start:', raw);
          }
          break;
        case 'message_delta':
          {
            if (parsedItem.delta.stop_reason === 'end_turn') {
              console.log('Claude is done with the chat.', parsedItem);
              // this.status = 'EndTurnReached';
            } else if (parsedItem.delta.stop_reason === 'stop_sequence') {
              console.log('Claude is stopping because it encountered a stop sequence');
            } else if (parsedItem.delta.stop_reason === 'max_tokens') {
              console.log('Claude is stopping because max tokens were exhausted');
              // this.status = 'MaxTokensUsed';
            } else if (parsedItem.delta.stop_reason === 'tool_use') {
              console.log('Claude is stopping because it needs a tool response');
              // this.status = 'ToolCallRequested';
            }
          }
          break;
        case 'message_stop':
          {
            console.log('stopping: ', parsedItem);
            liveMessageSubscriber(``, [], 
              {isMessageEnd: true, isToolEnd: true, forPlanner: this.forPlanner}
            );
          }
          break;
        case 'content_block_start':
          {
            this.contentBlockStatus = 'started';
            switch (parsedItem.content_block.type) {
              case 'text':
                {
                  this.textMessageContent = parsedItem.content_block.text;
                }
                break;
              case 'tool_use':
                {
                  const index = parsedItem.index || 0;
                  while (this.toolCalls.length <= parsedItem.index) {
                    this.toolCalls.push({
                      id: '',
                      tool: '',
                      partialInput: '',
                    });
                  }
                  this.toolCalls[index].id = parsedItem.content_block.id;
                  this.toolCalls[index].tool = parsedItem.content_block.name;
                }
                break;
              default:
                console.error('unknown delta in content_block');
            }
            // console.log(`${this.textMessageContent} ${JSON.stringify(this.toolCalls)}`)
            // console.log("stream chunk received from llm");
            liveMessageSubscriber(this.textMessageContent, this.toolCalls, 
              {isMessageEnd: false, isToolEnd: false, forPlanner: this.forPlanner}
            );
          }
          break;
        case 'content_block_delta':
          {
            switch (parsedItem.delta.type) {
              case 'input_json_delta':
                {
                  const index = parsedItem.index || 0;
                  while (this.toolCalls.length <= parsedItem.index) {
                    this.toolCalls.push({
                      id: '',
                      tool: '',
                      partialInput: '',
                    });
                  }
                  this.toolCalls[index].partialInput += parsedItem.delta.partial_json;
                }
                break;
              case 'text_delta':
                {
                  this.textMessageContent += parsedItem.delta.text;
                }
                break;
            }
            // console.log(`${this.textMessageContent} ${JSON.stringify(this.toolCalls)}`)
            // console.log("stream chunk received from llm");
            liveMessageSubscriber(this.textMessageContent, this.toolCalls, 
              {isMessageEnd: false, isToolEnd: false, forPlanner: this.forPlanner}
            );
          }
          break;
        case 'content_block_stop':
          {
            console.log('content block ended: ', parsedItem);
            this.contentBlockStatus = 'ended';
            liveMessageSubscriber(this.textMessageContent, this.toolCalls, 
              {isMessageEnd: false, isToolEnd: true, forPlanner: this.forPlanner}
            );
          }
          break;
        case 'error':
          {
            // alert('Model error!!');
            store.dispatch({
              type: "SET_AI_MODAL",
              payload: {
                visibility: false,
                prompt: "An error happened which may or may not have stopped the chat in the middle. The error was: " + JSON.stringify(parsedItem)
              }
            });
            console.error('The model error alert was shown due to: ', parsedItem);
            this.contentBlockStatus = 'ended';
            this.textMessageContent = parsedItem.details;
          }
          break;
        case 'soft_refresh':
          {
            this.contentBlockStatus = 'compile_request';
            reloadExternalPlugins({uuid: this.appId}).then(() => {
              store.dispatch(softRestartConfig());
            });
          }
          break;
        case 'compile_failed':
          {
            alert('Error during compilation');
            this.contentBlockStatus = 'compile_error';
            this.textMessageContent = 'An error happened during compilation: ' + JSON.stringify(parsedItem);
          }
          break;
        case 'compile_successful':
          {
            console.log('Plugins compiled' + raw);
          }
          break;
        default:
          console.error('unknown stream item: ', parsedItem);
      }
    } catch (err) {
      console.error('[AGENT] Failed to process streamItem: ', raw, err);
    }
  }

  getStatus() {
    let result = {
      msg: 'waiting...',
      program: '',
      toolCalls: [{id: '', tool: '', partialInput: ''}],
      contentBlockStatus: this.contentBlockStatus,
    };

    if (this.textMessageContent) {
      result.msg = this.textMessageContent;
      result.program = this.textMessageContent;
    }

    if (this.toolCalls.length > 0) {
      result.toolCalls = this.toolCalls;
    }

    return result;
  }
}

// Saga to handle chat completion
export function* runChatCompletionSaga(action: DispatchAction<RunChatCompletionRequestPayload>): SagaIterator {
  try {
    const {prompt, model, provider, completionUrl, appId, usePlanner, liveMessageSubscriber, onCompleted} =
      action.payload;

    // Clear any previous live message
    // yield put(clearLiveMessage());

    // Make the API request
    const loggedinUserEmail = store.getState()?.user?.user?.email ?? 'unknown';
    const loggedinUserId = store.getState()?.user?.user?.id ?? 'unknown';
    const response = yield call(fetch, completionUrl + `/provider/${provider}/model/${model}`, {
      method: 'POST',
      headers: {
        'content-type': 'application/json',
        'X-Apptile-User-Email': loggedinUserEmail,
        'X-Apptile-User-Id': loggedinUserId,
      },
      body: JSON.stringify({
        message: prompt,
        usePlanner,
      }),
    });

    if (!response.ok) {
      console.error('Network error in response: ', response);
      throw new Error(`API request failed with status ${response.status}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('text/plain')) {
      throw new Error(`Expected text/plain contentType in prompt response but got ${contentType}`);
    }

    // Get the response reader for streaming
    const reader = response.body.getReader();
    const streamProcessor = new StreamProcessor(appId, usePlanner);
    const decoder = new TextDecoder();

    // Process the stream
    try {
      // if (!usePlanner) {
      //   throw new TypeError("network error");
      // }
      while (true) {
        const {done, value} = yield call([reader, reader.read]);
        if (done) break;

        // Convert the chunk to text
        const chunk = decoder.decode(value, {stream: true});
        let prevOffset = streamProcessor.messages.length - 1;

        // Process the chunk
        streamProcessor.read(chunk, 0);

        // Process any new stream items
        if (streamProcessor.messages.length > prevOffset + 1) {
          for (let i = prevOffset + 1; i < streamProcessor.messages.length; ++i) {
            if (streamProcessor.messages[i]?.type === 'streamItem') {
              try {
                streamProcessor.processStreamItem(
                  streamProcessor.messages[i].text, 
                  liveMessageSubscriber
                );
              } catch (err) {
                console.error('Error processing stream item:', err);
              }
            }
          }
        }

        // Get the current status and update the live message
        // const status = streamProcessor.getStatus();
        // yield put(updateLiveMessage(status.msg + (status.toolCall ? JSON.stringify(status.toolCall) : '')));
      }
    } catch (streamError) {
      console.error("Sending error")
      if (streamError instanceof TypeError) {
        if (streamError.message.includes('network error')) {
          onCompleted({
            type: 'network error',
            error: streamError,
            message: 'Internet connetivity issue happened! Ask the model to continue?',
          });
          // alert("Network Disruption! The llm's connection to your device was dropped. You can wait for some time and ask it to continue.");
          return;
        } else {
          alert('Model error! TypeError but not network errror');
          // store.dispatch({
          //   type: "SET_AI_MODAL",
          //   payload: {
          //     visibility: false,
          //     prompt: "An error happened which may or may not have stopped the chat in the middle. The error was: " + streamError
          //   }
          // });
          console.error('Model error!', streamError);
        }
      } else {
        // alert('Model error!');
        yield put({
          type: "SET_AI_MODAL",
          payload: {
            visibility: false,
            prompt: "An error happened that may or may not have stopped the chat in the middle. The error was: " + streamError
          }
        });
        console.error('Model error processing stream:', streamError);
      }
    }

    // Process any remaining data
    try {
      const chunk = decoder.decode();
      let prevOffset = streamProcessor.messages.length - 1;
      streamProcessor.read(chunk, 0);

      if (streamProcessor.messages.length > prevOffset + 1) {
        for (let i = prevOffset + 1; i < streamProcessor.messages.length; ++i) {
          if (streamProcessor.messages[i]?.type === 'streamItem') {
            try {
              streamProcessor.processStreamItem(
                streamProcessor.messages[i].text, 
                liveMessageSubscriber
              );
            } catch (err) {
              console.error('Error processing remaining stream item:', err);
            }
          }
        }
      }
    } catch (streamError) {
      if (streamError instanceof TypeError) {
        if (streamError.message.includes('network error')) {
          onCompleted({
            type: 'network error',
            error: streamError,
            message: 'Internet connetivity issue happened! Ask the model to continue?',
          });
          // alert("Network Disruption! The llm's connection to your device was dropped. You can wait for some time and ask it to continue.");
          return;
        } else {
          alert('Model error! TypeError but not network errror');
          console.error('Model error!', streamError);
        }
      } else {
        // alert('Model error!');
        yield put({
          type: "SET_AI_MODAL",
          payload: {
            visibility: false,
            prompt: "An error happened that may or may not have stopped the chat in the middle. The error was: " + streamError
          }
        });
        console.error('Model error processing stream:', streamError);
      }
    }

    // Get the final status
    const status = streamProcessor.getStatus();

    // Handle tool calls if present
    const toolsSet = new Set();
    const nocodeLayerToolCalls = status.toolCalls.filter(it => {
      if (it.tool.startsWith('nocodelayer_')) {
        toolsSet.add(it.tool);
        return it.tool.startsWith('nocodelayer_');
      } else {
        return false;
      }
    });
    let result = {
      program: status.program,
      toolCall: nocodeLayerToolCalls,
      shouldSendToolResponse: nocodeLayerToolCalls.length > 0,
    };

    if (result.shouldSendToolResponse) {
      console.log('[AGENT] Tool call detected:', nocodeLayerToolCalls);
      try {
        // Execute the tool call if needed
        let toolResult: Array<{result: string; id: string}> = [];
        for (let i = 0; i < status.toolCalls.length; ++i) {
          const toolCall = status.toolCalls[i];
          if (toolsMap.hasOwnProperty(toolCall.tool) && toolCall.id) {
            console.log('[AGENT] running tool: ', status.toolCalls[i]);
            const tool = toolsMap[toolCall.tool];
            try {
              const inputVars = JSON.parse(toolCall.partialInput);
              inputVars.apptile_context = {
                appId,
                liveMessageSubscriber,
                model,
                provider,
              };
              console.log('[AGENT] yielding to tool');
              const result = yield * tool.handler(inputVars);
              console.log('[AGENT] received tool result', result);
              if (result === "CODE_GENERATION_TOOL_ERROR") { 
                yield put({
                  type: "SET_CHAT_RUNNING",
                  payload: false,
                });

                yield put({
                  type: "SET_AI_MODAL",
                  payload: {
                    visibility: true,
                    prompt: "It looks like network connection might have been lost during the operation of the plugin agent. Please check if the work is incomplete and proceed accordingly."
                  }
                });
                return;
              } else {
                toolResult.push({result, id: toolCall.id});
              }
            } catch (err) {
              console.log('[AGENT] handling tool errror');
              if (toolCall.id) {
                toolResult.push({result: 'Error: ' + err, id: toolCall.id});
              }
            }
          }
        }

        // If the tool requires a response, make another chat completion request
        console.log('[AGENT] Sending tool response back to LLM: ', toolResult);
        yield put({
          type: RUN_CHAT_COMPLETION_REQUEST,
          payload: {
            prompt: {
              isToolResponse: true,
              response: toolResult,
            },
            model,
            provider,
            completionUrl,
            appId,
            usePlanner,
            liveMessageSubscriber,
            onCompleted,
          },
        });
        return;
      } catch (err) {
        console.error('[AGENT] Tool execution failed', err);
      }
    } else {
      console.log('[AGENT] No tool call detected');
    }

    console.log('[AGENT] Dispatching success action with result:', JSON.stringify(result, null, 2));
    yield put(runChatCompletionSuccess(result));
    console.log('[AGENT] Chat completion saga completed successfully');
    yield yield put({
      type: APP_SAVE,
      payload: {
        newSave: false,
        showToast: true,
        remark: 'Save after prompt completion',
      },
    });
    onCompleted();
  } catch (error: any) {
    console.error('[AGENT] Error in chat completion:', error);
    yield put(runChatCompletionFailure(error?.message || 'Chat completion failed'));
    console.log('[AGENT] Chat completion saga failed');
    action.payload.onCompleted(error);
  }
}

// Handle the ADD_NAVIGATION_PAGE_WITH_AI_DONE action
export function* handleNavigationPageWithAIDone(
  action: DispatchAction<{screenName: string; pageName: string}>,
): SagaIterator {
  try {
    const {screenName} = action.payload;
    console.log('[AGENT] Navigation page with AI done, dispatching SCREEN_CREATION_DONE event for screen:', screenName);
    yield put(screenCreationDone(screenName));
  } catch (error) {
    console.error('[AGENT] Error handling navigation page with AI done:', error);
  }
}

// Handle the CREATE_PLUGIN_REQUEST action
export function* handleCreatePluginRequest(
  action: DispatchAction<{appId: string; pluginName: string; pluginPrompt: string; usePlanner: boolean}>,
): SagaIterator {
  const user = yield select(state => state.user);
  const usersLLM = user?.user?.llmModel ?? "";
  let model = DEFAULT_MODEL;
  let provider = DEFAULT_PROVIDER;
  if (usersLLM) {
    [provider, model] = usersLLM.split(",");
  }
  logger.info("Using llm: ", model, provider);
  debugger

  try {
    const {appId, pluginName, pluginPrompt, usePlanner} = action.payload;
    console.log('[AGENT] Handling CREATE_PLUGIN_REQUEST for plugin:', pluginName);

    // Make the plugin creation API call
    const pluginApiUrl = `${window.PLUGIN_SERVER_URL}/plugins/prompt/${appId}/${pluginName}/provider/${model}/model/${provider}`;
    console.log('[AGENT] Plugin API URL:', pluginApiUrl);

    try {
      yield call(fetch, pluginApiUrl, {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          labelPrefix: pluginName,
          listingName: pluginName,
          displayDescription: 'Basic plugin created from template',
          listingIcon: 'badge',
          pluginRegistryName: pluginName,
          message: pluginPrompt,
          usePlanner,
        }),
      });

      console.log('[AGENT] Plugin creation API call successful');

      // Dispatch the CREATE_PLUGIN_DONE event
      yield put({
        type: DispatchActions.CREATE_PLUGIN_DONE,
        payload: {
          pluginName,
          success: true,
        },
      });
    } catch (apiError) {
      console.error('[AGENT] Plugin creation API call failed:', apiError);

      // Dispatch the CREATE_PLUGIN_DONE event with error
      yield put({
        type: DispatchActions.CREATE_PLUGIN_DONE,
        payload: {
          pluginName,
          success: false,
          error: apiError,
        },
      });
    }
  } catch (error) {
    console.error('[AGENT] Error handling CREATE_PLUGIN_REQUEST:', error);
  }
}

// Handle the ADD_PLUGIN_TO_SCREEN_REQUEST action
export function* handleAddPluginToScreenRequest(
  action: DispatchAction<{screenName: string; pluginName: string}>,
): SagaIterator {
  try {
    const {screenName, pluginName} = action.payload;
    console.log('[AGENT] Handling ADD_PLUGIN_TO_SCREEN_REQUEST for plugin:', pluginName, 'to screen:', screenName);

    // Add the plugin to the screen
    yield put({
      type: 'ADD_PLUGIN',
      payload: {
        layout: {
          width: 50,
          height: 30,
        },
        pluginType: pluginName.replace(/[^a-zA-Z0-9]/g, ''),
        configType: 'widget',
        container: '',
        pageId: screenName,
        afterRefWidget: false,
        refWidget: '',
      },
    });

    console.log('[AGENT] Plugin added to screen');

    // Dispatch the ADD_PLUGIN_TO_SCREEN_DONE event
    yield put({
      type: DispatchActions.ADD_PLUGIN_TO_SCREEN_DONE,
      payload: {
        screenName,
        pluginName,
        success: true,
      },
    });
  } catch (error) {
    console.error('[AGENT] Error handling ADD_PLUGIN_TO_SCREEN_REQUEST:', error);

    // Dispatch the ADD_PLUGIN_TO_SCREEN_DONE event with error
    yield put({
      type: DispatchActions.ADD_PLUGIN_TO_SCREEN_DONE,
      payload: {
        screenName: action.payload.screenName,
        pluginName: action.payload.pluginName,
        success: false,
        error,
      },
    });
  }
}

// Handle the CREATE_GLOBAL_STATE_REQUEST action
export function* handleCreateGlobalStateRequest(
  action: DispatchAction<{stateName: string; stateValue: any; pluginType: "StatePlugin"|"LocalStoragePlugin";}>,
): SagaIterator {
  try {
    const {stateName, stateValue, pluginType} = action.payload;
    console.log('[AGENT] Handling CREATE_GLOBAL_STATE_REQUEST for state:', stateName, 'with value:', stateValue);

    // Add the state plugin
    yield put({
      type: 'ADD_PLUGIN',
      payload: {
        layout: undefined,
        pluginId: stateName,
        pluginType: pluginType,
        configType: 'state',
      },
    });

    yield delay(2000);
    // I don't know why a softrefresh is required to create the plugin's 
    // object in appModel. If the pluginCreated below can be made to be true
    // without a softrefresh we can remove these delays
    // store.dispatch(softRestartConfig());
    // yield delay(2000);
    let pluginCreated = false; // !!store.getState()?.appModel.values.get(stateName);
    let maxRetries = 4;
    while (!pluginCreated && (--maxRetries >= 0)) {
      store.dispatch(softRestartConfig());
      yield delay(2000);
      pluginCreated = !!store.getState()?.appModel.values.get(stateName);
    }

    if (!pluginCreated) {
      console.log('[AGENT] Failed to create global state! Not sure what will happen now');
    } else {
      console.log('[AGENT] State plugin added');
    }

    // Set the state value
    yield put({
      type: 'PLUGIN_UPDATE_CONFIG_PATH',
      payload: {
        pluginId: stateName,
        pageId: null,
        selector: ['config'],
        update: {
          value: stateValue,
          key: stateName
        },
      },
    });

    yield delay(1000);

    yield put({
      type: 'PLUGIN_TRIGGER_ON_UPDATE',
      payload: {
        plugin: {
          id: stateName, 
          pluginType: pluginType
        },
        pageLoad: false
      }
    });

    console.log('[AGENT] State value set');

    // Dispatch the CREATE_GLOBAL_STATE_DONE event
    yield put({
      type: DispatchActions.CREATE_GLOBAL_STATE_DONE,
      payload: {
        stateName,
        stateValue,
        success: true,
      },
    });
  } catch (error) {
    console.error('[AGENT] Error handling CREATE_GLOBAL_STATE_REQUEST:', error);

    // Dispatch the CREATE_GLOBAL_STATE_DONE event with error
    yield put({
      type: DispatchActions.CREATE_GLOBAL_STATE_DONE,
      payload: {
        stateName: action.payload.stateName,
        stateValue: action.payload.stateValue,
        success: false,
        error,
      },
    });
  }
}

/**
 * 
 * For running in console 
store.dispatch({
  type: 'TEST_TOOL_RUN',
  payload: {
    tool: 'nocodelayer_read_global_plugin_value',
    arguments: {
      plugin_name: 'todoState',
    },
    apptile_context: {
      appId: '2067a889-faa5-4b5a-9fa6-1fdacf18ae67',
      model: 'claude-3-7-sonnet-20250219',
      provider: 'claude'
    } 
  }
})

store.dispatch({
  type: 'TEST_TOOL_RUN',
  payload: {
    tool: 'nocodelayer_create_global_plugin',
    arguments: {
      name: 'todoState',
      value: '{{({a: 1})}}',
      pluginType: 'LocalStoragePlugin'
    },
    apptile_context: {
      appId: '2067a889-faa5-4b5a-9fa6-1fdacf18ae67',
      model: 'claude-3-7-sonnet-20250219',
      provider: 'claude'
    } 
  }
})
 */

function* handleDebugToolRun(action: {
  type: 'TEST_TOOL_RUN', 
  payload: {
    tool: string; 
    arguments: any;
    apptile_context: {
      appId: string;
      model: string;
      provider: string;
    }
  }
}): SagaIterator {
  if (toolsMap.hasOwnProperty(action.payload.tool)) {
    console.log('[AGENT] running tool: ', action.payload.tool);
    const tool = toolsMap[action.payload.tool];
    const toolResult = [];
    try {
      const inputVars = action.payload.arguments;
      inputVars.apptile_context = {
        appId: action.payload.apptile_context.appId,
        liveMessageSubscriber: console.log,
        model: action.payload.apptile_context.model,
        provider: action.payload.apptile_context.provider
      };
      console.log("[AGENT] yielding to tool");
      const result = yield *tool.handler(inputVars);
      console.log("[AGENT] received tool result", result);
      toolResult.push({result});
    } catch (err) {
      console.log("[AGENT] handling tool errror");
      toolResult.push({result: "Error: " + err});
    }
    console.log("[AGENT] tool result: ", toolResult);
  } else {
    console.log("[AGENT] tool not found");
  }
}

// Root AI saga
export default function* AISaga(): SagaIterator {
  yield all([
    takeEvery(RUN_CHAT_COMPLETION_REQUEST, runChatCompletionSaga),
    // takeLatest(FETCH_CHAT_HISTORY_REQUEST, fetchChatHistorySaga),
    takeLatest(DispatchActions.ADD_NAVIGATION_PAGE_WITH_AI_DONE, handleNavigationPageWithAIDone),
    takeLatest(DispatchActions.CREATE_PLUGIN_REQUEST, handleCreatePluginRequest),
    takeLatest(DispatchActions.ADD_PLUGIN_TO_SCREEN_REQUEST, handleAddPluginToScreenRequest),
    takeLatest(DispatchActions.CREATE_GLOBAL_STATE_REQUEST, handleCreateGlobalStateRequest),
    takeLatest('TEST_TOOL_RUN', handleDebugToolRun)
  ]);
}
