$__iconfont__data: map-merge(if(global_variable_exists('__iconfont__data'), $__iconfont__data, ()), (
	"ApptileWebIcons": (
		"AnalyticsP2A": "\ea01",
		"BuildP2A": "\ea02",
		"IntegrationsP2A": "\ea03",
		"NotificationsP2A": "\ea04",
		"SidebarP2A": "\ea05",
		"TRBL-button-top": "\ea06",
		"TRBL-button": "\ea07",
		"accordion": "\ea08",
		"alignment-bottom": "\ea09",
		"alignment-center": "\ea0a",
		"alignment-left": "\ea0b",
		"alignment-middle": "\ea0c",
		"alignment-right": "\ea0d",
		"alignment-top": "\ea0e",
		"analytics-outline": "\ea0f",
		"apptile-live": "\ea10",
		"authentication": "\ea11",
		"back-arrow": "\ea12",
		"badge": "\ea13",
		"bell-outline": "\ea14",
		"book-outline": "\ea15",
		"border-radius-left": "\ea16",
		"border-radius": "\ea17",
		"brands-outline": "\ea18",
		"button": "\ea19",
		"calender": "\ea1a",
		"checkMark": "\ea1b",
		"checkbox": "\ea1c",
		"circle-check": "\ea1d",
		"circle-outline": "\ea1e",
		"clock-refresh": "\ea1f",
		"clock": "\ea20",
		"code": "\ea21",
		"container": "\ea22",
		"database": "\ea23",
		"datasource": "\ea24",
		"delete": "\ea25",
		"down-arrow": "\ea26",
		"download-outline": "\ea27",
		"dropDownArrow": "\ea28",
		"edit-icon": "\ea29",
		"expression": "\ea2a",
		"gear-outline": "\ea2b",
		"go-live": "\ea2c",
		"help": "\ea2d",
		"house-outline": "\ea2e",
		"icon": "\ea2f",
		"image-carousel": "\ea30",
		"image-list": "\ea31",
		"image": "\ea32",
		"integration-disconnect": "\ea33",
		"integrations": "\ea34",
		"light": "\ea35",
		"list-view": "\ea36",
		"local-storage": "\ea37",
		"magnify": "\ea38",
		"menu-vertical": "\ea39",
		"modal": "\ea3a",
		"monetization": "\ea3b",
		"notifications": "\ea3c",
		"pages-outline": "\ea3d",
		"pages": "\ea3e",
		"payment": "\ea3f",
		"percentage-outline": "\ea40",
		"pills": "\ea41",
		"pin-fill": "\ea42",
		"pin-outline": "\ea43",
		"query": "\ea44",
		"radio-button": "\ea45",
		"rich-text": "\ea46",
		"security": "\ea47",
		"select": "\ea48",
		"settings": "\ea49",
		"slider-range": "\ea4a",
		"small-shop": "\ea4b",
		"snapshots-outline": "\ea4c",
		"star-rating": "\ea4d",
		"state": "\ea4e",
		"switch": "\ea4f",
		"text-input": "\ea50",
		"text": "\ea51",
		"themes": "\ea52",
		"tiles-old": "\ea53",
		"tiles": "\ea54",
		"timer": "\ea55",
		"toast-check": "\ea56",
		"upload": "\ea57",
		"vector-square": "\ea58",
		"video-player": "\ea59",
		"web-view": "\ea5a",
		"your-design": "\ea5b"
	)
));


$create-font-face: true !default; // should the @font-face tag get created?

// should there be a custom class for each icon? will be .filename
$create-icon-classes: true !default; 

// what is the common class name that icons share? in this case icons need to have .icon.filename in their classes
// this requires you to have 2 classes on each icon html element, but reduced redeclaration of the font family
// for each icon
$icon-common-class: 'icon' !default;

// if you whish to prefix your filenames, here you can do so.
// if this string stays empty, your classes will use the filename, for example
// an icon called star.svg will result in a class called .star
// if you use the prefix to be 'icon-' it would result in .icon-star
$icon-prefix: '' !default; 

// helper function to get the correct font group
@function iconfont-group($group: null) {
  @if (null == $group) {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  @if (false == map-has-key($__iconfont__data, $group)) {
    @warn 'Undefined Iconfont Family!';
    @return ();
  }
  @return map-get($__iconfont__data, $group);
}

// helper function to get the correct icon of a group
@function iconfont-item($name) {
  $slash: str-index($name, '/');
  $group: null;
  @if ($slash) {
    $group: str-slice($name, 0, $slash - 1);
    $name: str-slice($name, $slash + 1);
  } @else {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  $group: iconfont-group($group);
  @if (false == map-has-key($group, $name)) {
    @warn 'Undefined Iconfont Glyph!';
    @return '';
  }
  @return map-get($group, $name);
}

// complete mixing to include the icon
// usage:
// .my_icon{ @include iconfont('star') }
@mixin iconfont($icon) {
  $slash: str-index($icon, '/');
  $group: null;
  @if ($slash) {
    $group: str-slice($icon, 0, $slash - 1);
  } @else {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  &:before {
    font-family: $group;
    font-style: normal;
    font-weight: 400;
    content: iconfont-item($icon);
  }
}

// creates the font face tag if the variable is set to true (default)
@if $create-font-face == true {
  @font-face {
   font-family: "ApptileWebIcons";
   src: url('../webfonts/ApptileWebIcons.eot'); /* IE9 Compat Modes */
   src: url('../webfonts/ApptileWebIcons.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
      url('../webfonts/ApptileWebIcons.woff') format('woff'), /* Pretty Modern Browsers */
      url('../webfonts/ApptileWebIcons.ttf')  format('truetype'), /* Safari, Android, iOS */
      url('../webfonts/ApptileWebIcons.svg') format('svg'); /* Legacy iOS */
  }
}

// creates icon classes for each individual loaded svg (default)
@if $create-icon-classes == true {
  .#{$icon-common-class} {
    font-style: normal;
    font-weight: 400;

    @each $icon, $content in map-get($__iconfont__data, "ApptileWebIcons") {
      &.#{$icon-prefix}#{$icon}:before {
        font-family: "ApptileWebIcons";
        content: iconfont-item("ApptileWebIcons/#{$icon}");
      }
    }
  }
}
