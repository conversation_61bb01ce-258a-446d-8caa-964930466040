import React, {Ref, useCallback, useEffect, useRef, useState} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, Dimensions, ScaledSize} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {useDispatch, useSelector} from 'react-redux';

import ApptileApp from '../../../../app/ApptileApp';
import {store, ThemeContainer, DispatchActions} from 'apptile-core';
import NavigationEditorContainer from '../../navigation/containers/NavigationEditorContainer';
import ModelBrowserContainer from '../../modelBrowser/containers/ModelBrowserContainer';
import StyleModelBrowserContainer from '../../modelBrowser/containers/StyleModelBrowserContainer';
import PagesListControl from '../../pagesList/components/PagesListControl';
import PluginListControlContainer from '../../pluginList/containers/PluginListControlContainer';
import EditorRightPaneTabsContainer from '../containers/EditorRightPaneTabsContainer';
import {useParams} from '../../../routing.web';
import {HotKeys} from 'react-hotkeys';
import {HiddenPasteInput} from '../../../components/HiddenPasteInput';
import {HOTKEY_MAPS} from '../../../common/hotKeyMaps';
import AuthTopBanner from '../../auth/components/AuthTopBanner';
// import phoneBG from '../../../assets/images/phone-frame.png';
import {PlatformState} from '@/root/web/store/PlatformReducer';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import {useCallbackRef} from 'apptile-core';
import Animated from 'react-native-reanimated';
import ShopifyLeftPane from '../../../integrations/shopify/views/ShopifyLeftPane';
import ShopifyRightPane from '../../../integrations/shopify/views/ShopifyRightPane';
import {ApptileCanvasScaleContext} from 'apptile-core';
import ImageEditorContainer from '../../imageEditor/containers/ImageEditorContainer';
import LinkingEditor from '../../linkingEditor/LinkingEditor';
import GlobalAppSettingsEditor from '../../globalAppSettings/GlobalAppSettingsEditor';
import Button from '@/root/web/components-v2/base/Button';
import BlueprintsDialog from '../../blueprintsExport/blueprintsSaveDialog';
import {selectAppConfig} from 'apptile-core';
import {useIsEditable} from 'apptile-core';
import {initApptileIsEditable} from 'apptile-core';
import {CustomDragLayer} from '../../../components/CustomDragLayer';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import QRCode from 'react-qr-code';
import CollapsiblePanel from '@/root/web/components/CollapsiblePanel';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
import generatePreviewURL from '@/root/web/common/generatePreviewURL';
import EditorContext from '@/root/web/context/editorContext';
import MandatoryFields from './MandatoryFields';
import {
  deleteBindingError,
  toggleBindingErrors,
  toggleChatView,
  openPropertyInspector,
} from '@/root/web/actions/editorActions';
import {getNavigationContext} from 'apptile-core';
import {selectPlugin} from 'apptile-core';
import {Resizable} from 're-resizable';
import BetaBuildTag from '@/root/web/components/BetaBuildTag';
import {selectAppsById} from '@/root/web/selectors/AppSelectors';
import CodeEditor, {openCodeEditor} from '../../../../web/components/codeEditor/codeEditor';
import BindingEditor from '../../../../web/components/codeEditor/bindingEditor';
import IntegrationsApi from '@/root/web/api/IntegrationsApi';
import {Api} from '../../../../web/api/Api';
import {EDITOR_CLOSE_CODE_EDITOR} from '@/root/web/actions/editorActions';
import {APPTILE_API_ENDPOINT, APPTILE_UPDATE_ENDPOINT} from '../../../../.env.json';
import {EditorState} from '../../../common/webDatatypes';
import WebSDKBetaBuildTag from '@/root/web/components/WebSDKBetaBuildTag';
import SupabaseConnectButton from '@/root/web/integrations/supabase';
import {themeColors} from '../../../components/codeEditor/tokyonight';
import {cloneOpenApp} from '@/root/web/components/pluginServer';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {AIClientV2} from '@/root/web/components/codeEditor/aiClientV2';
import {usePersistentAIChat} from '@/root/web/components/codeEditor/PersistentAIChat';

const commonStyles = getCommonStyles();
const theme = getTheme();
interface EditorProps {
  editor: EditorState;
  platform: PlatformState;
  changeAppConfig: (appId: string, orgId: string, forkId: string | number, branchName?: string) => void;
  orgs: any;
  fetchOrgs: () => void;
  saveAppState: (
    newSave: boolean,
    showToast: boolean,
    message?: string,
    backup?: boolean,
    force?: boolean,
    saveWithCacheWithoutPublishing?: boolean,
  ) => void;
  updateApp: (publishedAppSaveId: number) => void;
  editorCopyAction: () => void;
  editorPasteAction: (pasteString: string) => void;
  configureDatasources: (appId: string, forceUpdateSecrets: boolean) => void;
  clearDatasourcesCredentials: () => void;
  softRestartConfig: () => void;
  changeAppContextData: (appId: string) => void;
  initApptileTilesMode: (isTilesOnly: boolean) => void;
  params: Record<string, string>;
}

function recurse(object: any, binding: string, path: string[]): undefined | string[] {
  if (object === binding) {
    return path;
  } else if (Array.isArray(object)) {
    for (let index = 0; index < object.length; ++index) {
      const result = recurse(object[index], binding, path.concat(index.toString()));
      if (result) {
        return result;
      }
    }
  } else if (object && typeof object === 'object') {
    for (let key in object) {
      const result = recurse(object[key], binding, path.concat(key));
      if (result) {
        return result;
      }
    }
  }
}

const CANVAS_SCALE = 0.8;
const APP_CANVAS_DIV_WIDTH = 445;
const APP_CANVAS_DIV_HEIGHT = 888;
const APP_CANVAS_BORDER_WIDTH = 4;

let progressIds = [] as string[];

const Editor: React.FC<EditorProps> = props => {
  const {
    platform,
    changeAppConfig,
    fetchOrgs,
    saveAppState,
    editorCopyAction,
    editorPasteAction,
    params,
    configureDatasources,
    clearDatasourcesCredentials,
    softRestartConfig,
    changeAppContextData,
    initApptileTilesMode,
    editor,
    orgs,
  } = props;
  const dispatch = useDispatch();
  const pasteInputNode: Ref<any> = useRef();
  const justForCompat = useRef(null);
  const isEmbeddedInShopify = platform?.isEmbeddedInShopify;
  const isEditable = useIsEditable();
  const isIntelligenceMode = editor.codeEditor.open;
  const toggleIsEditable = () => dispatch(initApptileIsEditable(!isEditable));
  const toggleErrorView = () => {
    dispatch(toggleBindingErrors());
  };
  const toggleChat = () => {
    dispatch(toggleChatView());
  };
  const [viewHeight, setViewHeight] = useState(Dimensions.get('window').height);
  const [viewWidth, setViewWidth] = useState(Dimensions.get('window').width);
  const focusedElMeta = useRef({
    id: "",
    pluginName: "",
    componentType: "",
    key: "",
    element: null as (Element|null)
  });
  // const av_scale = useSharedValue(0.75);
  // const styleAnimatedScale = useAnimatedStyle(() => {
  //   return {
  //     transform: [
  //       {
  //         scale: withSpring(av_scale.value, {restDisplacementThreshold: 0.001, restSpeedThreshold: 0.01}),
  //       },
  //     ],
  //   };
  // }, [av_scale]);
  const [isExportDialogOpen, setOpenDialog] = useState(false);
  const appConfig = useSelector(selectAppConfig);
  const apptile = useSelector((state: EditorRootState) => state.apptile);
  const pageKeysToId = useSelector(state => state.appModel.get('pageKeysToId'));
  const showBindingErrors = editor.showBindingErrors;
  const persistentChat = usePersistentAIChat();
  const showChat = editor.showChatView;

  // Handle persistent chat visibility
  useEffect(() => {
    if (showChat) {
      persistentChat.setShowChat(true);
      persistentChat.setIsVisible(true);
    } else {
      persistentChat.setIsVisible(false);
    }
  }, [showChat, persistentChat]);
  const bindingErrors = editor.bindingErrors;
  const elements = useRef<{
    leftPanel: null | HTMLDivElement;
    rightPanel: null | Resizable;
    appCanvas: null | Animated.View;
    hoveredBoundary: null | HTMLDivElement;
    selectedBoundary: null | HTMLDivElement;
    selectedLabel: null | HTMLDivElement;
  }>({
    leftPanel: null,
    rightPanel: null,
    appCanvas: null,
    hoveredBoundary: null,
    selectedBoundary: null,
    selectedLabel: null
  }).current;

  // useEffect(() => {
  //   if (isIntelligenceMode && canvasScale !== INTELLIGENCE_MODE_CANVAS_SCALE) {
  //     setCanvasScale(INTELLIGENCE_MODE_CANVAS_SCALE);
  //   }
  // }, [isIntelligenceMode]);

  const appsById = useSelector(selectAppsById);
  const windowListener = useCallback(({window, screen}: {window: ScaledSize; screen: ScaledSize}) => {
    setViewWidth(window.width);
    setViewHeight(window.height);
  }, []);

  useMountEffect(() => {
    const appId = params?.id ?? 1;
    const orgId = params?.orgId;
    const forkId = params?.forkId;
    const branchName = params?.branchName;
    changeAppConfig(appId, orgId, forkId, branchName);
    fetchOrgs();
    Dimensions.addEventListener('change', windowListener);
    dispatch(initApptileIsEditable(false));

    // Check if we should automatically open the chat view
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('openChat') === 'true' && !editor.showChatView) {
      // Use a small timeout to ensure the editor is fully loaded
      setTimeout(() => {
        console.log('Auto-opening chat view from URL parameter');
        dispatch(toggleChatView());
      }, 1000);
    }
  });

  // useEffect(() => {
  //   av_scale.value = canvasScale;
  // }, [av_scale, canvasScale]);

  useEffect(() => {
    initApptileTilesMode(false);
  }, []);

  useEffect(() => {
    function narrowToDiv(canvas: any): canvas is HTMLDivElement {
      return !!canvas?.getBoundingClientRect?.call;
    }

    const appCanvas = narrowToDiv(elements.appCanvas) && elements.appCanvas;
    const hoveredElementBoundary = narrowToDiv(elements.hoveredBoundary) && elements.hoveredBoundary;
    const selectedElementBoundary = narrowToDiv(elements.selectedBoundary) && elements.selectedBoundary;
    const selectedElementLabel = narrowToDiv(elements.selectedLabel) && elements.selectedLabel;

    const showBoundary = (ev: MouseEvent) => {
      // const scaleFactor = canvasScale > 0 ? (1/canvasScale) : 1;
      const canvasRect = {top: 0, left: 0};
      if (appCanvas) {
        const rect = appCanvas.getBoundingClientRect();
        canvasRect.left = rect.left;
        canvasRect.top = rect.top;
      }

      if ((ev.target instanceof Element)) {
        const id = ev.target.getAttribute("id")
        const rect = ev.target.getBoundingClientRect();

        if (id && hoveredElementBoundary) {
          hoveredElementBoundary.setAttribute("style", `
            position: absolute;
            display: block;
            width: ${rect.width / CANVAS_SCALE}px;
            height: ${rect.height / CANVAS_SCALE}px;
            top: ${((rect.top - canvasRect.top) / CANVAS_SCALE) - 4}px; 
            left: ${((rect.left - canvasRect.left) / CANVAS_SCALE) - 4}px;
            border: solid 2px #005be4;
            border-radius: 1px;
            pointer-events: none;
            box-sizing: border-box;
          `);
        }
      }
    };

    const selectElement = (ev: MouseEvent) => {
      if (ev.metaKey || ev.ctrlKey) {
        ev.stopPropagation();
        ev.preventDefault();

        if ((ev.target instanceof Element)) {
          const id = ev.target.getAttribute("id")
          const rect = ev.target.getBoundingClientRect();
          if (id) {
            const parts = id.split("-");
            focusedElMeta.current = {
              id,
              pluginName: parts[0],
              componentType: parts[1],
              key: parts.slice(2).join("-"),
              element: ev.target
            };

            const canvasRect = {top: 0, left: 0};
            if (appCanvas) {
              const rect = appCanvas.getBoundingClientRect();
              canvasRect.left = rect.left;
              canvasRect.top = rect.top;
            }

            if (selectedElementBoundary) {
              selectedElementBoundary.setAttribute("style", `
                position: absolute;
                display: block;
                width: ${rect.width / CANVAS_SCALE}px;
                height: ${rect.height / CANVAS_SCALE}px;
                top: ${((rect.top - canvasRect.top) / CANVAS_SCALE) - 4}px; 
                left: ${((rect.left - canvasRect.left) / CANVAS_SCALE) - 4}px;
                border: solid 2px #005be4;
                border-radius: 1px;
                box-sizing: border-box;
              `);
            }

            if (selectedElementLabel && id) {
              selectedElementLabel.innerText = focusedElMeta.current.componentType;
            }
          }
        }
      }
    };

    const hideElementBoundary = (ev: MouseEvent) => {
      if (hoveredElementBoundary) {
        hoveredElementBoundary.setAttribute('style', 'display: none;');
      }
    }

    if (appCanvas) {
      appCanvas.addEventListener('mouseover', showBoundary);
      appCanvas.addEventListener('click', selectElement, true);
      appCanvas.addEventListener('mouseleave', hideElementBoundary);
    }

    return () => {
      if (appCanvas) {
        appCanvas.removeEventListener('mouseover', showBoundary);
        appCanvas.removeEventListener('click', selectElement);
        appCanvas.removeEventListener('mouseleave', hideElementBoundary);
      }
    }
  }, [elements, focusedElMeta])

  const selectPluginWithTarget = useCallback(() => {
    const reduxState = store.getState();
    // const pluginSubtype = focusedElMeta.current.pluginName;
    const selectedElement = focusedElMeta.current.element;
    const pluginRootEl = selectedElement?.closest('[id^="rootElement-"]');
    const pluginIdAttr = pluginRootEl?.getAttribute('id');
    const pageKey = reduxState.activeNavigation.activePageKey;
    const pluginId = pluginIdAttr?.substring("rootElement-".length);
    if (pluginId) {
      store.dispatch({
        type: DispatchActions.SELECT_PLUGIN_WITH_TARGET,
        payload: {
          selector: [pageKey, pluginId],
          target: focusedElMeta.current.id
        }
      });
    } else {
      alert("Could not find the plugin to edit!");
    }
    setTimeout(() => {
      const prompterInput = document.querySelector('#prompter-input');
      if (prompterInput) {
        prompterInput.innerHTML = prompterInput.innerHTML + `<br/>The following prompt is in regards to the plugin: <span style="color: #ff9e64;">${focusedElMeta.current.pluginName}</span>. 
        The specific element inside the plugin that I'm talking about has the id: <span style="color: #9ece6a">${focusedElMeta.current.id}</span>.
        `;
      }
    }, 500)
  }, [focusedElMeta]);

  const addContextToChat = useCallback(() => {
    const reduxState = store.getState();
    const isChatRunning = reduxState?.ai?.isChatRunning ?? false;
    if (isChatRunning) {
      alert("Please wait for the current run to finish!")
    } else {
      store.dispatch(openChatView());
      setTimeout(() => {
        const prompterInput = document.querySelector('#prompter-input');
        if (prompterInput) {
          prompterInput.innerHTML = prompterInput.innerHTML + `<br/>The following prompt is in regards to the plugin: <span style="color: #ff9e64;">${focusedElMeta.current.pluginName}</span>. 
          The specific element inside the plugin that I'm talking about has the id: <span style="color: #9ece6a">${focusedElMeta.current.id}</span>.
          `;
        }
      }, 500)
      // dispatch action to open chat window
      // trigger the chat from the dom
    }
  }, [focusedElMeta]);

  const appId = params?.id;
  useEffect(() => {
    if (appId) {
      changeAppContextData(appId);
    }
  }, [appId, changeAppContextData]);

  useEffect(() => {
    function openEditor(pluginName: string, appId: string) {
      openCodeEditor(
        {
          appId,
          artefactName: pluginName,
          type: 'plugin',
        },
        dispatch,
      );
    }

    const enableVim = localStorage.getItem('enablevim');
    const cliMessageHandler = async ev => {
      console.log('received evnt from cli: ', ev.data, ev.source);
      switch (ev.data.type) {
        case 'requestAppData':
          const p1 = Api.get(`${APPTILE_API_ENDPOINT}/api/v2/app/${appId}/manifest`).then(res => res.data);
          const p2 = IntegrationsApi.fetchAppIntegrations(appId);
          const p3 = Api.get(`${APPTILE_API_ENDPOINT}/api/v2/app/${appId}/bundles`)
            .then(res => res.data)
            .catch(err => {
              console.error('Bundles load failed for apptile-cli', err);
              return [];
            });

          const [manifest, appIntegrations, bundles] = await Promise.all([p1, p2, p3]);

          ev.source.postMessage(
            {
              type: 'appData',
              payload: {
                manifest,
                appIntegrations: appIntegrations.data,
                apptileConfig: {
                  apptileServer: APPTILE_API_ENDPOINT,
                  appconfigServer: APPTILE_UPDATE_ENDPOINT,
                },
                bundles,
              },
            },
            '*',
          );
          break;
        case 'requestAppConfigSave':
          // dispatch app_save with saveWithCacheWithoutPublishing
          const NEW_SAVE = true;
          const SHOW_TOAST = true;
          const REMARK = ev.data.message || 'Saving from cli';
          const BACKUP = false;
          const FORCE = false;
          const SAVE_WITH_CACHE_AND_DONT_PUBLISH = true;
          dispatch(saveAppState(NEW_SAVE, SHOW_TOAST, REMARK, BACKUP, FORCE, SAVE_WITH_CACHE_AND_DONT_PUBLISH));
          break;
        case 'setWebSdkArtifactId':
          const artifactId = ev.data.artifactId;
          localStorage.setItem('web-sdk-artifact-id', artifactId);
          window.location.reload();
          break;
        default:
          console.log('Received event which is not processable: ', ev);
          break;
      }
    };

    const vimCmdHandler = ev => {
      const cmdLine = document.createElement('dialog');
      cmdLine.setAttribute('id', 'vimcmdline');
      const input = document.createElement('input');

      if (ev.keyCode === 58) {
        input.value = '';
        input.setAttribute('style', 'position: fixed; bottom: 0; left: 0;');
        cmdLine.appendChild(input);
        document.body.appendChild(cmdLine);
        input.addEventListener('change', ev => {
          document.body.removeChild(cmdLine);
          const command = ev.target?.value;
          console.log('command: ', command);
          if (command && command.startsWith(':ed ')) {
            openEditor(command.slice(4), appId);
          } else if (command && command.startsWith(':q')) {
            dispatch({
              type: EDITOR_CLOSE_CODE_EDITOR,
            });
          } else if (command && command.startsWith(':cli')) {
            const container = document.querySelector('#apptile-cli');
            if (container) {
              if (container.style.transform) {
                container.style.transform = '';
              } else {
                container.style.transform = 'translateX(-40vw)';
              }
            }
          }
        });
        input.focus();
        cmdLine.show();
      }
    };
    if (enableVim) {
      window.addEventListener('message', cliMessageHandler);
      window.addEventListener('keypress', vimCmdHandler);
      return () => {
        window.removeEventListener('keypress', vimCmdHandler);
        window.removeEventListener('message', cliMessageHandler);
      };
    }
  }, [appId]);

  const apptileState = useSelector(state => state.apptile);
  const [previewURL, setPreviewURL] = useState<string>();
  useEffect(() => {
    const appId = apptileState?.appId as string;
    const orgId = apptileState?.orgId as string;
    const forkId = apptileState?.forkId;
    const branchName = apptileState?.appBranch;
    const appName = orgs?.appsById[params.id]?.name;
    const orgName = orgs?.orgsById[params.orgId]?.name;
    setPreviewURL(generatePreviewURL(orgId, appId, forkId, branchName, appName, orgName));
  }, [apptileState, orgs, params.id, params.orgId]);

  const getHotkeyHandlers = useCallbackRef(() => {
    return {
      EDITOR_PASTE: () => {
        logger.info('Pasting');
        pasteInputNode?.current.focus();
      },
      EDITOR_COPY: editorCopyAction,
      PUBLISH: () => {
        let message = window && window.prompt('Please enter publish message', 'Updated template');
        if (message) saveAppState(true, true, message || 'Updated template');
      },
      SAVE: () => {
        console.log("Page has been saved using 's' key");
        saveAppState(false, true);
      },
      SOFT_REFRESH: () => {
        console.log("Soft refresh has been triggered using 'r' key");
        softRestartConfig();
      },
      TOGGLE_EDIT: () => {
        console.log("Toggle edit mode has been triggered using 'e' key");
        toggleIsEditable();
      },
    };
  });

  // const onLayout = useCallback(
  //   (e: LayoutChangeEvent) => {
  //     // setViewWidth(e.nativeEvent.layout.width);
  //     // setViewHeight(e.nativeEvent.layout.height);
  //     // const sc = (e.nativeEvent.layout.height * 0.9) / 946;
  //     const sc = (viewHeight * 0.9) / 946;
  //     if (isIntelligenceMode) {
  //       setCanvasScale(INTELLIGENCE_MODE_CANVAS_SCALE);
  //     } else {
  //       setCanvasScale(sc);
  //     }
  //     logger.info('Canvas Scale: ', sc);
  //   },
  //   [viewHeight, isIntelligenceMode],
  // );

  const onRefreshDatasourceCredentials = () => {
    const appId = params?.id ?? null;
    if (appId) {
      configureDatasources(appId, true);
    }
  };

  const onClearDsCredentials = () => {
    clearDatasourcesCredentials();
  };

  const onSoftRefreshEditor = () => {
    const appId = params?.id ?? null;
    if (appId) {
      softRestartConfig(appId, true);
    }
  };

  const onCloseDialog = useCallback(() => {
    setOpenDialog(false);
  }, []);

  const onResolve = useCallback(
    (binding: string) => {
      dispatch(deleteBindingError(binding));
    },
    [dispatch],
  );

  // useEffect(() => {
  //   if (appId) cloneOpenApp(appId);
  // }, [appId]);

  const onLocate = useCallback(
    (binding: string) => {
      console.log('Finding: ' + binding);
      let result: any;
      parentLoop: for (let [pageKey, page] of appConfig.get('pages').entries()) {
        const plugins = page.get('plugins');
        for (let [pluginKey, plugin] of plugins.entries()) {
          const path = recurse(plugin.toJS(), binding, []);
          if (path) {
            result = [pageKey, pluginKey, path];
            break parentLoop;
          }
        }
      }
      console.log('locate finished');
      if (!result) {
        alert('oops! unable to locate this binding');
      } else {
        const context = getNavigationContext();
        for (let key of pageKeysToId.keys()) {
          if (pageKeysToId.get(key) === result[0]) {
            const finalPath = [key, 'plugins', result[1]];
            dispatch(selectPlugin(finalPath));
            dispatch(openPropertyInspector());
            context.navigate(result[0]);
            return;
          }
        }

        logger.info('Attempting to naviate for ', result);
        context.navigate(result[0]);
        const dialog = document.createElement('dialog');
        dialog.innerHTML = `
        <div>
          <div style="max-width: 500px">
            Navigating to the page where this error might be located. Please wait and try again once this dialog closes.
          </div>
        </div>`;
        document.body.appendChild(dialog);
        dialog.showModal();
        setTimeout(() => {
          dialog.close();
          document.body.removeChild(dialog);
        }, 2000);
      }
    },
    [dispatch, appConfig, pageKeysToId],
  );

  const [bindingToSearch, setBindingToSearch] = useState('');

  let errorList = null;
  let chatView = null;
  if (showBindingErrors) {
    errorList = [];
    let bindingErrorEntries: Array<[string, {error: string}]> = Array.from(bindingErrors.entries());
    bindingErrorEntries.sort((a, b) => {
      if (b[1].error.startsWith('TypeError')) {
        return -1;
      } else {
        return 1;
      }
    });

    errorList.push(
      <View>
        <Text>Search a binding</Text>
        <input type="text" value={bindingToSearch} onChange={ev => setBindingToSearch(ev.target.value)} />
        <Button onPress={() => onLocate(bindingToSearch)}>Find</Button>
      </View>,
    );

    for (let [binding, error] of bindingErrorEntries) {
      errorList.push(
        <View
          key={binding}
          style={{
            paddingTop: '10px',
            paddingBottom: '10px',
            paddingLeft: '2px',
            paddingRight: '2px',
            borderStyle: 'solid',
            borderWidth: '1px',
            marginTop: '2px',
            marginBottom: '2px',
            borderRadius: '6px',
          }}>
          <View style={{display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginBottom: '10px'}}>
            <Button onPress={() => onLocate(binding)}>Find</Button>
            <Button onPress={() => onResolve(binding)}>Dismiss</Button>
          </View>
          <Text>{binding}</Text>
          <Text style={{color: 'red', fontFamily: 'monospace', fontSize: 10, overflow: 'scroll', textWrap: 'nowrap'}}>
            {error.error}
          </Text>
          <Text
            style={{
              fontSize: 10,
              marginTop: '9px',
              color: '#ccc',
              fontFamily: 'monospace',
              overflow: 'scroll',
              textWrap: 'nowrap',
            }}>
            {error.transpiledFn}
          </Text>
        </View>,
      );
    }

    if (errorList.length == 0) {
      errorList.push(
        <View>
          <Text>No errors so far. Use the navigation tab, generate cache or reload to look for errors.</Text>
        </View>,
      );
    }
  } else if (showChat) {
    // Chat is handled by persistent chat provider
    chatView = null;
  }

  let editorComponent = null;
  if (editor.codeEditor.open) {
    editorComponent = (
      <CodeEditor
        editorState={editor.codeEditor}
        phoneContainerWidth={CANVAS_SCALE * APP_CANVAS_DIV_WIDTH}
      />
    );
  } else if (editor.bindingEditor.open) {
    editorComponent = <BindingEditor editorState={editor.bindingEditor} />;
  }

  let leftPanel = null;
  if (!isEmbeddedInShopify && !isIntelligenceMode) {
    leftPanel = (
      <Resizable
        defaultSize={{height: '100vh', width: 280}}
        minWidth={280}
        maxWidth="50vw"
        enable={{
          top: false,
          right: true,
          bottom: false,
          left: false,
          topRight: false,
          bottomRight: false,
          bottomLeft: false,
          topLeft: false,
        }}
        // style={{ overflow: 'scroll' }}
      >
        <View style={[styles.leftPanel]} id="left-panel" ref={el => (elements.leftPanel = el)}>
          <AuthTopBanner />
          <Text style={[commonStyles.baseText, {textAlign: 'center'}]}>
            ({appsById[params?.id]?.name ?? 'App name'})
          </Text>
          <View style={styles.leftPanelTop}>
            <View style={styles.rowContainer}>
              <Button
                containerStyles={{flex: 1, margin: 4}}
                variant="PILL"
                color="SECONDARY"
                size="SMALL"
                onPress={() => saveAppState(false, true)}>
                Save
              </Button>
              {/* <View style={styles.ml4}>
              <PublishHistory
                appSaves={orgs?.appsById[params.id]?.appSaves}
                onRevert={id => updateApp(id)}
                publishedAppSaveId={orgs?.appsById[params.id]?.publishedAppSaveId}
                currentAppSaveId={orgs?.appsById[params.id]?.currentAppSaveId}
              />
            </View>
            <View style={styles.ml4}>
              <PublishButton onPress={message => saveAppState(true, true, message)} />
            </View>
            <View style={styles.ml4}>
              <PreviewButton
                appName={orgs?.appsById[params.id]?.name}
                orgName={orgs?.orgsById[params.orgId]?.name}
              />
            </View> */}
              <Button
                containerStyles={{margin: 4}}
                variant="PILL"
                color="SECONDARY"
                size="SMALL"
                icon="reload"
                onPress={() => onSoftRefreshEditor()}
              />
              <Button
                containerStyles={{margin: 4}}
                variant="PILL"
                color="SECONDARY"
                size="SMALL"
                icon="content-copy"
                onPress={() => editorCopyAction()}
              />
              <Button
                containerStyles={{margin: 4}}
                variant={isEditable ? 'FILLED-PILL' : 'PILL'}
                color="SECONDARY"
                icon={isEditable ? 'pencil-off' : 'pencil'}
                size="SMALL"
                onPress={() => toggleIsEditable()}
              />
              <Button
                containerStyles={{margin: 4}}
                variant={showBindingErrors ? 'FILLED-PILL' : 'PILL'}
                color="SECONDARY"
                icon="bug"
                size="SMALL"
                onPress={() => toggleErrorView()}
              />
              <Button
                containerStyles={{margin: 4}}
                variant={showChat ? 'FILLED-PILL' : 'PILL'}
                color="SECONDARY"
                icon="creation"
                size="SMALL"
                onPress={() => toggleChat()}
              />
              <Button
                containerStyles={{margin: 4}}
                variant="PILL"
                color="SECONDARY"
                size="SMALL"
                icon="export"
                onPress={() => setOpenDialog(true)}
              />
              {isExportDialogOpen && (
                <BlueprintsDialog
                  isOpen={isExportDialogOpen}
                  blueprintUUID={appConfig.get('blueprintUUID')}
                  onClose={onCloseDialog}
                />
              )}
            </View>
            {/* <View style={styles.rowContainer}>
            <TouchableOpacity style={[styles.navButton, styles.ml4]} onPress={() => onSoftRefreshEditor()}>
              <Text style={{color: 'white', textAlign: 'center'}}>Soft Restart Editor</Text>
            </TouchableOpacity>
          </View> */}
            <Button
              containerStyles={{margin: 4}}
              variant="PILL"
              color="SECONDARY"
              size="SMALL"
              onPress={() =>
                window.open(
                  `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/editor`,
                  '_self',
                )
              }>
              Editor V2
            </Button>
            <Button
              containerStyles={{margin: 4}}
              variant="PILL"
              color="SECONDARY"
              size="SMALL"
              onPress={() =>
                window.open(
                  `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/builds/dashboard`,
                  '_self',
                )
              }>
              Build Dashboard
            </Button>
            <PopoverComponent
              trigger={
                <View style={[styles.navButton, {margin: 8}]}>
                  <Text style={{color: 'white', textAlign: 'center'}}>Preview App</Text>
                </View>
              }>
              <View style={styles.QRPopover}>
                {previewURL && <QRCode size={220} style={QRCodeStyle} value={previewURL} viewBox={`0 0 220 220`} />}
                <Text style={[commonStyles.baseText, {textAlign: 'center'}]}>Scan to view on phone</Text>
              </View>
            </PopoverComponent>
            <View style={{marginHorizontal: 8}}>
              <CollapsiblePanel key={'Mandatory Fields'} title="Mandatory Fields" isOpen={false}>
                <MandatoryFields />
              </CollapsiblePanel>
            </View>
            <View style={{marginHorizontal: 8}}>
              <CollapsiblePanel key={'DS Creds'} title="DS Creds" isOpen={false}>
                <TouchableOpacity style={styles.navButton} onPress={() => onRefreshDatasourceCredentials()}>
                  <Text style={{color: 'white', textAlign: 'center'}}>Force Refresh DS Creds</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.navButton} onPress={() => onClearDsCredentials()}>
                  <Text style={{color: 'white', textAlign: 'center'}}>Clear DS Credentials</Text>
                </TouchableOpacity>
              </CollapsiblePanel>
            </View>
            {/* <TouchableOpacity style={styles.navButton} onPress={() => navigate('integrations')}>
            <Text style={{color: 'white', textAlign: 'center'}}>Go to Integration Dashboard</Text>
          </TouchableOpacity>  */}
          </View>
          <ScrollView style={{flex: 1}}>
            <View style={[styles.leftScrollContainer]}>
              <View style={{height: 'auto', flex: 1}}>
                <PagesListControl />
                <NavigationEditorContainer />
                <LinkingEditor />
                <GlobalAppSettingsEditor />
                <ModelBrowserContainer />
                <StyleModelBrowserContainer />
                <PluginListControlContainer />
                <ImageEditorContainer />
              </View>
            </View>
          </ScrollView>
        </View>
      </Resizable>
    );
  } else if (isEmbeddedInShopify) {
    leftPanel = <ShopifyLeftPane />;
  }

  let rightPanel = null;
  if (!isEmbeddedInShopify && !isIntelligenceMode) {
    rightPanel = (
      <Resizable
        ref={el => (elements.rightPanel = el)}
        id="right-panel"
        defaultSize={{height: '100vh', width: 375}}
        minWidth={375}
        maxWidth="50vw"
        enable={{
          top: false,
          right: false,
          bottom: false,
          left: true,
          topRight: false,
          bottomRight: false,
          bottomLeft: false,
          topLeft: false,
        }}
        // style={{ overflow: 'scroll' }}
      >
        <View style={styles.rightPanel}>
          <EditorRightPaneTabsContainer />
        </View>
      </Resizable>
    );
  } else if (isEmbeddedInShopify) {
    rightPanel = <ShopifyRightPane />;
  }

  const cliui = (
    <div
      id="apptile-cli"
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        width: '40vw',
        height: '50vh',
        transition: 'transform 200ms',
        transform: 'translateX(-40vw)',
      }}>
      {
        /**/
        <iframe src={window.PLUGIN_SERVER_URL + '/public/ui/workspaces'} style={{width: '100%', height: '100%'}} />
        /**/
      }
      {/*
      <iframe
        src="http://localhost:3000/plugin-server/public/ui/workspaces"
        style={{width: '100%', height: '100%'}}
      />
      */}
    </div>
  );

  let intelligenecModePhoneStyles = {};
  if (isIntelligenceMode) {
    intelligenecModePhoneStyles = {
      position: 'fixed',
      right: -50,
    };
  }

  const CloneAndOpenButton = useCallback(() => {
    return <button onClick={() => cloneOpenApp(appId)}>Clone and open</button>;
  }, [appId]);

  return (
    <EditorContext.Provider value={{layout: 'editorV1'}}>
      <HotKeys component={'div'} keyMap={HOTKEY_MAPS.EDITOR_GLOBAL} handlers={getHotkeyHandlers()}>
        <ThemeContainer>
          <View style={[styles.editorLayout, {height: viewHeight, width: viewWidth}]}>
            <CustomDragLayer />
            <HiddenPasteInput
              ref={pasteInputNode}
              onPaste={(event: any) => {
                const pasteString = event.clipboardData.getData('text');
                editorPasteAction(pasteString);
              }}
            />
            {editorComponent}
            {leftPanel}
            <div
              id="appPreviewContainer"
              style={{
                display: showBindingErrors || showChat ? 'grid' : 'static',
                gridTemplateColumns: showBindingErrors || showChat ? '2fr 1fr' : '1fr',
                zIndex: 1,
              }}>
              <View style={{maxHeight: '100%', overflow: 'scroll'}}>
                {errorList}
                {chatView}
              </View>
              <View style={{height: '100%', flex: 1, justifyContent: 'center'}}>
                <Animated.View
                  style={[styles.appContainer, intelligenecModePhoneStyles]}
                  id="app-canvas"
                  ref={el => (elements.appCanvas = el)}>
                  <ApptileCanvasScaleContext.Provider value={1}>
                    {/* <Image source={phoneBG} style={[StyleSheet.absoluteFill, styles.deviceBezel]} /> */}
                    <View style={styles.appCanvas}>
                      <ApptileApp />
                    </View>
                  </ApptileCanvasScaleContext.Provider>
                  <div
                    id="hovered-element-boundary"
                    ref={el => (elements.hoveredBoundary = el)}
                    style={{display: 'none'}}
                  />
                  <div
                    ref={el => (elements.selectedBoundary = el)}
                    id="selected-element-boundary"
                    style={{display: 'none'}}>
                    <div
                      style={{
                        position: 'relative',
                        fontSize: 10,
                        background: '#005be4',
                        color: 'white',
                        padding: 2,
                        width: '100%',
                        top: -17,
                        left: -2,
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        minWidth: 180,
                      }}>
                      <div ref={el => (elements.selectedLabel = el)} id="element-label" />
                      <div
                        style={{
                          flexDirection: 'row',
                          display: 'flex',
                        }}>
                        <div
                          style={{
                            height: 13,
                            fontSize: 10,
                            paddingRight: 2,
                            cursor: 'pointer',
                            marginLeft: 6,
                            marginRight: 6
                          }}
                          onClick={selectPluginWithTarget}>
                          Edit
                        </div>
                        <div
                          style={{
                            height: 13,
                            fontSize: 10,
                            paddingRight: 2,
                            cursor: 'pointer',
                            marginLeft: 6,
                            marginRight: 6
                          }}
                          onClick={addContextToChat}>
                          Prompt
                        </div>
                        <div
                          style={{
                            height: 12,
                            fontSize: 10,
                            paddingRight: 2,
                            paddingLeft: 2,
                            border: 'solid 1px white',
                            cursor: 'pointer',
                            marginLeft: 6
                          }}
                          onClick={() => {
                            if (elements?.selectedBoundary) {
                              elements.selectedBoundary.setAttribute('style', 'display: none;');
                              focusedElMeta.current.id = '';
                            }
                          }}>
                          x
                        </div>
                      </div>
                    </div>
                  </div>
                </Animated.View>
              </View>
            </div>
            {rightPanel}
          </View>
        </ThemeContainer>
      </HotKeys>
      <BetaBuildTag />
      <WebSDKBetaBuildTag />
      {cliui}
      <View
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
        }}>
        <SupabaseConnectButton />
        <CloneAndOpenButton />
      </View>
    </EditorContext.Provider>
  );
};

const styles = StyleSheet.create({
  editorLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'stretch',
    alignContent: 'stretch',
    overflow: 'hidden',
  },
  deviceBezel: {
    backgroundColor: 'transparent',
    shadowColor: 'black',
    shadowOffset: {width: 10, height: 10},
    shadowRadius: 50,
    shadowOpacity: 0.4,
    overflow: 'visible',
  },
  leftPanel: {
    flex: 1,
    // maxWidth: 280,
    minWidth: 280,
    flexDirection: 'column',
    shadowColor: '#000000',
    alignItems: 'stretch',
    alignContent: 'stretch',
    flexGrow: 1,
    shadowOffset: {
      width: 5,
      height: 0,
    },
    shadowRadius: 10,
    shadowOpacity: 0.3,
    height: '100%',
  },
  leftPanelTop: {
    height: 'auto',
    flex: 1,
    flexBasis: 'auto',
    flexGrow: 0,
  },
  leftPanelAdj: {
    height: 'auto',
    flex: 1,
    flexBasis: 'auto',
    flexGrow: 0,
    position: 'absolute',
    top: 20,
    right: -48,
    width: 48,
    maxWidth: 48,
  },
  rowContainer: {
    height: 'auto',
    flex: 1,
    flexDirection: 'row',
    flexBasis: 'auto',
    flexGrow: 0,
    alignItems: 'stretch',
    marginLeft: 0,
  },
  leftScrollContainer: {
    flex: 1,
    padding: 2,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    fontSize: 12,
    flexBasis: 'auto',
  },
  appContainer: {
    position: 'relative',
    width: APP_CANVAS_DIV_WIDTH + (2 * APP_CANVAS_BORDER_WIDTH),
    height: APP_CANVAS_DIV_HEIGHT + (2 * APP_CANVAS_BORDER_WIDTH),
    borderRadius: 50,
    flexGrow: 0,
    flexShrink: 0,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    alignSelf: 'center',
    overflow: 'visible',
    backgroundColor: 'rgba(0,0,0,0)',
    borderWidth: APP_CANVAS_BORDER_WIDTH,
    borderColor: "black",
    transform: [{scale: CANVAS_SCALE}]
  },
  appCanvas: {
    top: 0,
    left: 0,
    width: APP_CANVAS_DIV_WIDTH,
    height: APP_CANVAS_DIV_HEIGHT,
    position: 'absolute',
    borderRadius: 45,
    overflow: 'hidden',
    flexGrow: 0,
    flexShrink: 0,
  },
  rightPanel: {
    flex: 1,
    // maxWidth: 375,
    minWidth: 375,
    height: '100%',
    flexDirection: 'column',
    shadowColor: '#000000',
    shadowOffset: {
      width: -5,
      height: 0,
    },
    shadowRadius: 10,
    shadowOpacity: 0.3,
  },
  ml4: {marginLeft: 4},
  navButton: {
    flex: 1,
    backgroundColor: 'rgb(33, 150, 243)',
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginBottom: 2,
    borderRadius: 5,
  },
  QRPopover: {
    backgroundColor: theme.TILE_BACKGROUND,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 15,
    borderWidth: 1,
  },
});

const QRCodeStyle = {
  padding: 12,
  background: '#FFFFFF',
  borderRadius: 12,
  marginTop: 12,
  marginBottom: 4,
};

function withParams(Component: any) {
  return (props: JSX.IntrinsicAttributes) => <Component {...props} params={useParams()} />;
}

export default withParams(Editor);
