import React, {useState} from 'react';
import {StyleSheet, Text, View, Image, Pressable} from 'react-native';
import LeftSidebar from '../dashboard/LeftSidebar';
import theme from '../styles-prompt-to-app/theme';
import {useParams, useNavigate} from 'react-router';

interface NotificationsPageProps {}

const NotificationsPage: React.FC<NotificationsPageProps> = () => {
  const params = useParams();
  const navigate = useNavigate();
  const [hovered, setHovered] = useState(false);

  return (
    <View style={styles.mainContainer}>
      <LeftSidebar mainBar="APP_EDITOR" />
      <View style={styles.comingSoonContainer}>
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          zIndex: 0,
          backgroundColor: '#121212',
          height: '100dvh',
          width: '100dvw',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <div
          style={{
            height: 300,
            width: 300,
            backgroundColor: '#0DACFF',
            filter: 'blur(100px)',
          }}
        />
      </div>
        <View style={{width: 684, height: 296, flexDirection: "column", alignItems: "center"}}>
          <Image
            style={{width: 77, height: 82, resizeMode: "cover", marginBottom: 35}}
            source={{uri: require("../../../assets/images/notificationbell.png")}}
          />
          <Text style={styles.descriptionText}>
            Set up push notifications to engage your App users. Dashboard unlocks once you generate a build
          </Text>
          <Pressable
            onHoverIn={() => {
              setHovered(true);
            }}
            onHoverOut={() => {
              setHovered(false);
            }}
            style={{
              backgroundColor: hovered ? theme.HOVER_COLOR : '#182232',
              height: 32,
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 58,
              borderRadius: theme.CORNER_RADIUS_HALF,
              paddingHorizontal: theme.PADDING_SMALL
            }}
            onPress={() => {
              navigate(
                `/dashboard/${(params as any)?.orgId}/app/${(params as any)?.id}/f/${(params as any)?.forkId}/b/${
                  (params as any)?.branchName
                }/publish/dashboard/publish`,
              )
            }}
          >
            <Text 
              style={{
                fontFamily: 'General Sans',
                fontWeight: '500',
                fontSize: 18,
                color: '#FAFAFA',
              }}
            >
              Generate Build
            </Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    minHeight: '100vh',
  },
  container: {
    flex: 1,
    padding: 20,
    // backgroundColor: theme.DEFAULT_BACKGROUND,
  },
  comingSoonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    backgroundColor: '#34363E',
    backgroundImage: `radial-gradient(circle, #4a4a4a 1px, transparent 1px)`,
    backgroundSize: '20px 20px'
  },
  comingSoonText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.FOREGROUND,
    marginBottom: 16,
  },
  descriptionText: {
    fontSize: 24,
    lineHeight: 35,
    fontWeight: '500',
    fontFamily: 'General Sans',
    color: theme.FOREGROUND,
    textAlign: 'center',
  },
});

export default NotificationsPage;
