import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface CustomModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  showCloseButton?: boolean;
  animationType?: 'slide' | 'fade' | 'none';
  transparent?: boolean;
  backdropOpacity?: number;
  children: React.ReactNode;
  modalStyle?: object;
  contentStyle?: object;
  closeOnBackdropPress?: boolean;
}

const CustomModal = ({
  visible,
  onClose,
  title,
  showCloseButton = true,
  animationType = 'fade',
  transparent = true,
  backdropOpacity = 0,
  children,
  modalStyle,
  contentStyle,
  closeOnBackdropPress = true,
}: CustomModalProps) => {
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.8));

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={transparent}
      animationType={animationType}
      onRequestClose={onClose}
      statusBarTranslucent>
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <Animated.View
          style={[
            styles.backdrop,
            {
              backgroundColor: `rgba(0, 0, 0, ${backdropOpacity})`,
              opacity: fadeAnim,
              ...(Platform.OS === 'web' && {
                backdropFilter: 'blur(8px)',
                WebkitBackdropFilter: 'blur(8px)',})
            },
          ]}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <Animated.View
              style={[
                styles.modalContainer,
                modalStyle,
                {
                  transform: [{scale: scaleAnim}],
                  opacity: fadeAnim,
                },
              ]}>
              <ScrollView  showsHorizontalScrollIndicator={false} showsVerticalScrollIndicator={false} style={[styles.content, contentStyle]}>{children}</ScrollView>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // paddingHorizontal: 20,
  },
  modalContainer: {
    backgroundColor:'transparent',
    // borderRadius: 2,
    // maxWidth: screenWidth - 40,
    borderWidth:1,
    borderColor: '#3D3D3D',
    maxHeight: screenHeight * 0.7,
    minHeight:screenHeight*0.3,
    width: '50%',
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 10,
    // },
    // shadowOpacity: 0.3,
    // shadowRadius: 20,
    // elevation: 10,
    // borderWidth: 1,
    // borderColor: '#3a3a3a',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // paddingHorizontal: 20,
    // paddingVertical: 16,
    // borderBottomWidth: 1,
    // borderBottomColor: "#3a3a3a",
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#3a3a3a',
    alignItems: 'center',
    justifyContent: 'center',
    // marginLeft: 12,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#ccc',
    fontWeight: '500',
  },
  content: {
    // padding: 20,
    // maxHeight: screenHeight * 0.6,
  },
});

export default CustomModal;
