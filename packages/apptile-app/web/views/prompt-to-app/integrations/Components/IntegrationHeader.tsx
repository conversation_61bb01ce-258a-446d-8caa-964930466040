import React from 'react';
import { StyleSheet, View,Text, TouchableOpacity, ActivityIndicator } from "react-native";
import LogoRender from "./LogoRender";
import TagWithIcon from "./TagWithIcon";
import theme from "../../styles-prompt-to-app/theme";

export const getPriceBadgeStyle = (priceType: string) => {
    switch (priceType) {
      case 'free':
        return styles.priceBadgeFree;
      case 'paid':
        return styles.priceBadgePaid;
      case 'coming-soon':
        return styles.priceBadgeComingSoon;
      default:
        return undefined;
    }
  };
const IntegrationHeader: React.FC<any> = ({integration, isConnected, isLoading, onClick}) => {
    const priceBadgeStyle = getPriceBadgeStyle(integration.priceType);
    return (
      <View style={styles.integrationCard}>
        <View style={styles.cardContent}>
        <LogoRender integration={integration} style={styles.iconStyles}/>
          <View style={styles.integrationInfo}>
            <View style={styles.integrationHeader}>
              <Text style={styles.integrationName}>{integration.title}</Text>
              {!isConnected ? (
                  <Text style={priceBadgeStyle ? [styles.priceBadge, priceBadgeStyle] : styles.priceBadge}>
                    {integration.price}
                  </Text>
                ) : (
                  <View style={styles.activeStatusContainer}>
                    <View style={styles.dot} />
                    <Text style={styles.activeStatusText}>Active</Text>
                  </View>
                )}
            </View>
            <Text style={styles.integrationDescription}>{integration.excerpt}</Text>
            <View style={styles.tags}>
              {integration?.tags?.map((tag, index) => (
                <TagWithIcon key={index} tag={tag} />
              ))}
            </View>
          </View>
        </View>
       {!isConnected && <TouchableOpacity
            style={styles.connectButton}
            onPress={() => {onClick()}}
            disabled={isLoading}
            >
            {isLoading &&  <ActivityIndicator color={theme.FOREGROUND} size={16} style={styles.loader}/>}
            <Text style={styles.connectButtonText}>{ isLoading ? 'Authenticating...' : 'Enable'}</Text>
          </TouchableOpacity>}
      </View>
    );
  };
  

  const styles = StyleSheet.create({
    loader:{
      marginHorizontal:4
    },
    mainContainer: {
      flex: 1,
      flexDirection: 'row',
      backgroundColor: theme.SIDEBAR_BACKGROUND,
      minHeight: '100vh',
    },
    integrationsSection: {
      width: '80%',
      alignSelf: 'center',
      justifyContent: 'center',
      alignContent: 'center',
      marginBottom: 'clamp(20px , 4vw, 40px)',
    },
    integrationCard: {
      backgroundColor: 'transparent',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginVertical: 12,
    },
    cardContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 16,
      flex: 1,
    },
    integrationLogo: {
      width: 'clamp(40px, 8vw, 125px)',
      height: 'clamp(40px, 8vw, 125px)',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.2)',
      marginRight: 16,
    },
    logoSymbol: {
      fontSize: 'clamp(10px, 2vw, 24px)',
    },
    integrationInfo: {
      flex: 1,
    },
    integrationHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    integrationName: {
      fontSize: 'clamp(10px, 2vw, 24px)',
      fontWeight: '600',
      fontFamily: 'General Sans',
      color: '#EFEFEF',
      letterSpacing: 0.48,
    },
  
    priceBadge: {
      paddingVertical: 'clamp(2px, 2vw, 4px)',
      paddingHorizontal: 'clamp(6px, 2vw, 12px)',
      borderRadius: 20,
      fontSize: 'clamp(4px, 2vw, 12px)',
      color: '#EFEFEF',
      fontFamily: 'General Sans',
      fontWeight: '500',
      letterSpacing: 0.32,
      marginHorizontal: 'clamp(6px, 2vw, 14px)',
    },
    priceBadgeFree: {
      backgroundColor: '#007AFF',
    },
    priceBadgePaid: {
      backgroundColor: '#007AFF',
    },
    priceBadgeComingSoon: {
      backgroundColor: '#000',
      borderWidth: 1,
      borderColor: '#4E4E4E',
    },
    integrationDescription: {
      color: '#EFEFEF',
      fontSize: 'clamp(8px, 2vw, 16px)',
      marginBottom: 12,
      colorL: '#EFEFEF',
      fontFamily: 'General Sans',
      fontWeight: '400',
      letterSpacing: 0.36,
    },
  
    tags: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 'clamp(2px, 2vw, 4px)',
    },
    tag: {
      backgroundColor: '#3D3D3D',
      color: '#e2e8f0',
      paddingVertical: 'clamp(2px, 2vw, 6px)',
      paddingHorizontal: 'clamp(4px, 2vw, 6px)',
      borderRadius: 20,
      fontSize: 'clamp(8px, 2vw, 16px)',
      fontWeight: '500',
      marginRight: 8,
      marginBottom: 4,
    },
    detailsButton: {
      backgroundColor: 'transparent',
      borderWidth: 0,
      color: '#EFEFEF',
      fontSize: 'clamp(8px, 2vw, 16px)',
      fontFamily: 'General Sans',
      fontWeight: '400',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
      letterSpacing: 0.4,
    },
    activeStatusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderColor: '#19D885',
      borderWidth: 1,
      borderRadius: 20,
      alignSelf: 'flex-start',
      backgroundColor: 'transparent',
      paddingVertical: 'clamp(2px, 2vw, 4px)',
      paddingHorizontal: 'clamp(6px, 2vw, 12px)',
      marginHorizontal: 'clamp(6px, 2vw, 14px)',
    },
    dot: {
      width: 'clamp(4px, 2vw, 8px)',
      height: 'clamp(4px, 2vw, 8px)',
      borderRadius: 9,
      backgroundColor: '#19D885',
      marginRight: 'clamp(4px, 2vw, 8px)',
    },
    activeStatusText: {
      color: '#19D885',
      fontSize: 'clamp(4px, 2vw, 12px)',
      fontWeight: '500',
      letterSpacing: 0.32,
      fontFamily: 'General Sans',
    },
    backcontainer: {
      width: '5%',
      backgroundColor: 'transparent',
      paddingLeft: '10%',
      alignSelf: 'flex-start',
      marginVertical: 20,
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    arrow: {
      fontSize: 18,
      color: '#EFEFEF',
      marginRight: 8,
      fontWeight: '400',
    },
    text: {
      fontSize: 16,
      color: '#EFEFEF',
      fontWeight: '400',
    },
    container: {
      flex: 1,
      // paddingHorizontal: 35,
      paddingVertical: 20,
      // alignItems: 'center',
      backgroundColor: '#121212',
      minHeight: '100vh',
      width: '100%',
      padding: 'clamp(20px, 5vw, 54px)',
    },
    containerwrap: {
      flexDirection: 'column',
      alignItems: 'center',
      flex: 1,
      backgroundColor: '#121212',
    },
    container2: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 20,
      borderRadius: 12,
      marginHorizontal: 16,
      marginVertical: 8,
    },
    iconStyles: {
      width: 'clamp(60px, 8vw, 120px)',
      height: 'clamp(60px, 8vw, 120px)',
      aspectRatio: 1 / 1,
      borderRadius: 12,
      resizeMode: 'contain',
      borderWidth: 1,
      borderColor: '#3D3D3D',
    },
    leftSection: {
      marginRight: 16,
    },
    logo: {
      width: 60,
      height: 60,
      backgroundColor: '#1a1a1a',
      borderRadius: 12,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      borderWidth: 1,
      borderColor: '#3a3a3a',
    },
    logoIcon: {
      fontSize: 24,
      color: '#4ade80', // Green color for the arrow
    },
    middleSection: {
      flex: 1,
      marginRight: 16,
    },
    titleRow: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginBottom: 8,
    },
    title: {
      fontSize: 20,
      fontWeight: '600' as const,
      color: 'white',
      marginRight: 12,
    },
    freeBadge: {
      backgroundColor: '#4285f4',
      paddingHorizontal: 12,
      paddingVertical: 4,
      borderRadius: 16,
    },
    freeBadgeText: {
      color: 'white',
      fontSize: 12,
      fontWeight: '500' as const,
    },
    description: {
      color: '#ccc',
      fontSize: 14,
      lineHeight: 20,
      marginBottom: 12,
    },
    databaseTag: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      backgroundColor: '#3a3a3a',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: '#4a4a4a',
      alignSelf: 'flex-start' as const,
    },
    databaseIcon: {
      fontSize: 14,
      marginRight: 6,
    },
    databaseText: {
      color: '#ccc',
      fontSize: 12,
      fontWeight: '500' as const,
    },
    rightSection: {},
    connectButton: {
      backgroundColor: '#1060E0',
      paddingHorizontal: 48,
      paddingVertical: 12,
      borderRadius: 8,
      flexDirection:'row'
    },
    connectButtonText: {
      fontSize: 16,
      fontWeight: '500',
      textAlign: 'center',
      fontFamily: 'General Sans',
      color: '#FFFFFF',
    },
      spinner: {
    width: 16,
    height: 16,
    border: '2px solid rgba(255, 255, 255, 0.3)',
    borderTop: '2px solid white',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    marginRight: 8,
  },
    tabsSection: {
      // flex: 1,
      display: 'flex',
      alignItems: 'flex-start',
      alignSelf: 'flex-start',
      marginBottom: 'clamp(10px, 2vw, 28px)',
    },
    tabs: {
      flexDirection: 'row',
      backgroundColor: '#222528',
      borderRadius: 'clamp(5px, 2vw, 9px)',
    },
    tab: {
      backgroundColor: 'transparent',
      borderWidth: 0,
      paddingVertical: 'clamp(6px, 2vw, 12px)',
      paddingHorizontal: 'clamp(8px, 2vw,24px)',
      borderRadius: 'clamp(4px, 2vw, 8px)',
      fontSize: 'clamp(10px, 2vw, 16px)',
      fontWeight: '500',
    },
    active: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.1)',
    },
    tabText: {
      color: '#EFEFEF',
      fontSize: 'clamp(8px, 2vw, 16px)',
      fontWeight: '500',
      letterSpacing: 0.32,
    },
    activeTabText: {
      color: '#EFEFEF',
    },
  });
  export default IntegrationHeader;