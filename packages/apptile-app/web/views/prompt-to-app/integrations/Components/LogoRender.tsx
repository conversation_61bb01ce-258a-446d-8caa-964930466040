import {Image, StyleSheet, View} from 'react-native'
import database from '../../../../assets/icons/database.svg';
import security from '../../../../assets/icons/security.svg';
import subscribe from '../../../../assets/icons/light.svg';
import vector from '../../../../assets/icons/vector-square.svg';
import upload from '../../../../assets/icons/upload.svg';
import functions from '../../../../assets/icons/code.svg';
import circleCheck from '../../../../assets/icons/circle-check.svg';
import authentication from '../../../../assets/icons/authentication.svg';
import monetization from '../../../../assets/icons/monetization.svg';
import payment from '../../../../assets/icons/payment.svg';
import Inapppurchase from '../../../../assets/images/Inapppurchase.png';
import Pexels from '../../../../assets/images/pexels.png';

const iconMap: any = {
  database,
  security,
  subscribe,
  vector,
  upload,
  functions,
  circleCheck,
  authentication,
  monetization,
  payment,
  Inapppurchase,
  Pexels,
}

const LogoRender = ({ integration, style }: any) => {
  const combinedStyles = { ...styles.imgStyle, ...style };

  const src = integration?.logo && iconMap?.[integration.logo]
    ? iconMap[integration.logo]
    : integration?.icon;

  if (!src) {
    return null;
  }

  return <View style={styles.imgContainer}><img src={src} style={combinedStyles} alt={integration?.logo || 'icon'} /></View>;
};
export const styles = StyleSheet.create({
  imgStyle:{
    width: 'clamp(40px, 8vw, 75px)',
    height: 'clamp(40px, 8vw, 75px)',
    aspectRatio: '1 / 1',
    borderRadius: 12,
    objectFit: 'contain',
  },
  imgContainer:{
    borderWidth:1, borderColor:'#3D3D3D', borderRadius: 12,
  }

})

export default LogoRender;