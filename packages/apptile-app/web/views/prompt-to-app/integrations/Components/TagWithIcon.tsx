import React from "react";
import { View, Text, StyleSheet } from "react-native";
import IconRender from "./IconRender";
const TagWithIcon: React.FC<any> = ({tag}) => (
    <View style={tagIconStyles.container}>
      <IconRender icon={tag?.iconName} size={14} />
      <Text style={tagIconStyles.text}>{tag?.name}</Text>
    </View>
  );
  
  const tagIconStyles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'flex-start',
      marginRight: 8,
      marginBottom: 4,
      backgroundColor: '#3D3D3D',
      borderRadius: 20,
      paddingVertical: 'clamp(2px, 1vw, 6px)',
      paddingHorizontal: 'clamp(4px, 1vw, 12px)',
    },
    icon: {
      marginRight: 6,
      fontSize: 'clamp(6px, 2vw, 12px)',
      color: '#e2e8f0',
    },
    text: {
      fontSize: 'clamp(6px, 2vw, 12px)',
      fontWeight: '500',
      color: '#e2e8f0',
      letterSpacing: 0.28,
      marginHorizontal: 4,
    },
  });

  export default TagWithIcon;
