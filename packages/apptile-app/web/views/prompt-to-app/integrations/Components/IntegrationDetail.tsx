import { StyleSheet, View, Text,ScrollView, TouchableOpacity, Linking } from "react-native";
import IconRender from "./IconRender";


const supportLinks = [
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path
          d="M21 11V10C21 6.229 21 4.343 19.76 3.172C18.519 2 16.522 2 12.53 2H11.47C7.479 2 5.482 2 4.24 3.172C3 4.343 3 6.229 3 10V14C3 17.771 3 19.657 4.24 20.828C5.481 22 7.478 22 11.47 22H12M8 7H16M8 12H13"
          stroke="#EFEFEF"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M21 20.647V17C21 15.57 19.657 14 18 14C16.343 14 15 15.57 15 17V20.5C15 21.28 15.733 22 16.636 22C17.54 22 18.273 21.28 18.273 20.5V17.765"
          stroke="#EFEFEF"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    title: 'Tile docs:',
    subtitle: 'Building with Cool Agent',
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path
          d="M11.1 3.00195C7.45195 3.00895 5.53995 3.09795 4.31995 4.31895C3.00195 5.63695 3.00195 7.75795 3.00195 12C3.00195 16.242 3.00195 18.363 4.31995 19.681C5.63795 20.999 7.75795 20.999 12 20.999C16.242 20.999 18.363 20.999 19.681 19.681C20.902 18.461 20.991 16.549 20.998 12.901M20.48 3.51695L14.932 9.05095M15.956 3.07895C16.659 3.06895 19.987 3.02295 20.481 3.51695C20.975 4.01195 20.929 7.34295 20.919 8.04695"
          stroke="#EFEFEF"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    title: 'Docs:',
    subtitle: 'Getting API keys / Edge Functions guide',
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path
          d="M20 9C19.205 5.015 15.513 2 11.08 2C6.064 2 2 5.855 2 10.61C2 12.895 2.938 14.97 4.469 16.51C4.805 16.85 5.03 17.313 4.939 17.79C4.79116 18.5657 4.45227 19.2922 3.953 19.904C5.26146 20.1411 6.61162 19.933 7.788 19.313C8.2 19.095 8.407 18.987 8.552 18.965C8.654 18.949 8.787 18.964 9 19"
          stroke="#EFEFEF"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M11 16.262C11 19.167 13.463 21.523 16.5 21.523C16.858 21.523 17.2127 21.4903 17.564 21.425C17.817 21.378 17.943 21.354 18.031 21.367C18.119 21.381 18.244 21.447 18.494 21.58C19.2 21.956 20.024 22.089 20.817 21.941C20.5136 21.567 20.308 21.1233 20.219 20.65C20.164 20.358 20.3 20.075 20.505 19.868C21.46 18.9106 21.9974 17.6143 22 16.262C22 13.356 19.537 11 16.5 11C13.463 11 11 13.356 11 16.262Z"
          stroke="#EFEFEF"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    url: 'https://discord.gg/eNKHZMMB',
    title: 'Community:',
    subtitle: '#Tile channel on Tile Discord',
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path
          d="M2 6L8.913 9.917C11.462 11.361 12.538 11.361 15.087 9.917L22 6"
          stroke="#EFEFEF"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M2.01581 13.4756C2.08081 16.5406 2.11381 18.0736 3.24481 19.2086C4.37581 20.3446 5.94981 20.3836 9.09881 20.4626C11.0388 20.5126 12.9608 20.5126 14.9008 20.4626C18.0498 20.3836 19.6238 20.3446 20.7548 19.2086C21.8858 18.0736 21.9188 16.5406 21.9848 13.4756C22.0048 12.4896 22.0048 11.5096 21.9848 10.5236C21.9188 7.4586 21.8858 5.9256 20.7548 4.7906C19.6238 3.6546 18.0498 3.6156 14.9008 3.5366C12.9671 3.4878 11.0325 3.4878 9.09881 3.5366C5.94981 3.6156 4.37581 3.6546 3.24481 4.7906C2.11381 5.9256 2.08081 7.4586 2.01481 10.5236C1.99376 11.5075 1.99476 12.4917 2.01581 13.4756Z"
          stroke="#EFEFEF"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
    url: '<EMAIL>',
    title: 'Email:',
    subtitle: '<EMAIL>',
  },
];
const IntegrationDetail = ({integration}: any) => {
    const {capabilities} = integration;
    const {permissions} = integration;

    const openUrl = (url:any) => {
      // If it's an email address, open mail client
      if (/^[\w-.]+@[\w-]+\.[\w-.]+$/.test(url)) {
        Linking.openURL(`mailto:${url}`);
      } else if (!/^https?:\/\//i.test(url)) {
        // If it doesn't start with http/https, add https
        Linking.openURL(`https://${url}`);
      } else {
        Linking.openURL(url);
      }
    };

    return (
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.mainContent}>
            {/* Left Column */}
            <View style={styles.leftColumn}>
              {/* Overview Section */}
              <View style={styles.card}>
                <Text style={styles.sectionTitle}>Overview</Text>
                <Text style={styles.overviewText}>{integration?.overViewText}</Text>
                <View style={styles.includesSection}>
                  <Text style={styles.includesTitle}>Includes:</Text>
                  <Text style={styles.includesText}>{integration?.includes}</Text>
                </View>
              </View>
  
              {/* Key Capabilities Section */}
              <View style={styles.card}>
                <Text style={styles.sectionTitle}>Key Capabilities</Text>
                <View style={styles.capabilitiesGrid}>
                  {capabilities.map((capability, index) => (
                    <View key={index} style={styles.capabilityCard}>
                      <View style={styles.capabilityHeader}>
                        <Text style={styles.capabilityIcon}>
                          <IconRender icon={capability.icon} />
                        </Text>
                        <View style={{width: '100%', flex: 1, alignItems: 'stretch', justifyContent: 'space-between'}}>
                          <Text style={styles.capabilityTitle}>{capability.title}</Text>
                          <Text style={styles.capabilityDescription}>{capability.description}</Text>
                        </View>
                      </View>
  
                      {/* <Text style={styles.capabilityDescription}>{capability.description}</Text> */}
                    </View>
                  ))}
                </View>
              </View>
  
              {/* Permissions Section */}
              <View style={styles.card}>
                <Text style={styles.sectionTitle}>Permissions & Data Access</Text>
                <View style={styles.permissionsList}>
                  {permissions.map((permission, index) => (
                    <View key={index} style={styles.permissionItem}>
                      <View style={styles.checkbox}>
                        <IconRender icon={'circleCheck'} />
                      </View>
                      <Text style={styles.permissionText}>{permission.permission}</Text>
                    </View>
                  ))}
                </View>
                <View style={styles.credentialsNote}>
                  <Text style={styles.lockIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M18.4999 11.4996C18.2249 10.4746 16.9749 8.99555 15.0749 9.09955C15.0749 9.09955 13.1429 8.99555 11.1909 8.99555C9.23786 8.99555 8.25086 9.02455 6.72486 9.09955C5.69986 9.09955 4.07486 9.67455 3.44986 11.4246C2.87386 13.1746 2.89986 16.7986 3.24986 18.6496C3.32486 19.5996 3.84786 20.9496 5.39986 21.6496C6.14986 22.0996 10.8499 21.9496 11.4999 21.9996M6.51586 8.19555C6.46586 5.82055 6.36586 3.94555 9.11986 2.39455C10.0459 2.01955 11.4229 1.69455 13.1249 2.49455C14.9019 3.56955 15.1229 4.70755 15.2779 4.99455C15.7029 6.12055 15.4779 7.72055 15.5279 8.37055"
                        stroke="#FAFAFA"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M15.67 18.444C15.97 18.588 16.342 18.96 16.522 19.26C16.582 19.68 16.882 18.06 18.346 17.1M21 18C21 19.0609 20.5786 20.0783 19.8284 20.8284C19.0783 21.5786 18.0609 22 17 22C15.9391 22 14.9217 21.5786 14.1716 20.8284C13.4214 20.0783 13 19.0609 13 18C13 16.9391 13.4214 15.9217 14.1716 15.1716C14.9217 14.4214 15.9391 14 17 14C18.0609 14 19.0783 14.4214 19.8284 15.1716C20.5786 15.9217 21 16.9391 21 18Z"
                        stroke="#FAFAFA"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </Text>
                  <Text style={styles.credentialsText}>
                    Credentials are encrypted with Tile's KMS; revoke at any time from Project → Integrations
                  </Text>
                </View>
              </View>
  
              {/* <View>
              <Text>Ready to power your app with a fully-managed Postgres backend?</Text>
              <TouchableOpacity
            style={styles.connectButton}
            onPress={() => {
              navigate(`../connect/${integration.integrationCode}`);
            }}>
            <Text style={styles.connectButtonText}>Connect</Text>
          </TouchableOpacity>
  
            </View> */}
            </View>
  
            {/* Right Column */}
            <View style={styles.rightColumn}>
              <View style={styles.card}>
                <Text style={styles.sectionTitle}>Support & Documentation</Text>
                <View style={styles.supportLinks}>
                  {supportLinks.map((link, index) => (
                    <View
                      style={[
                        {borderBottomColor: '#2a2a2a'},
                        index !== supportLinks.length - 1 ? {borderBottomWidth: 1} : {borderWidth: 0},
                      ]}>
                      <TouchableOpacity key={index} disabled={!link.url} onPress={() => openUrl(link.url)} style={styles.supportLink} activeOpacity={0.7}>
                        <View style={styles.linkContent}>
                          <Text style={styles.linkIcon}>{link.icon}</Text>
                          <View style={styles.linkTextContainer}>
                            <Text style={styles.linkTitle}>{link.title}</Text>
                            <Text style={styles.linkSubtitle}>{link.subtitle}</Text>
                          </View>
                        </View>
                        <Text style={styles.arrow}>
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path
                              d="M10 16L14 12L10 8"
                              stroke="#EFEFEF"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                        </Text>
                      </TouchableOpacity>
                      <View style={{borderColor: 'red', borderWidth: '1'}} />
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </View>
      </ScrollView>
    );
  };


  const styles = StyleSheet.create({
    container: {
      //   flex: 1,
      //   backgroundColor: "#1a1a1a",
      width: '100%',
      paddingBottom: '2%',
    },
    mainContent: {
      flexDirection: 'row',
      //   padding: 16,
      gap: 24,
    },
    leftColumn: {
      flex: 6,
      gap: 24,
    },
    rightColumn: {
      flex: 4,
      //   maxWidth: 350,
    },
    card: {
      backgroundColor: '#1A1D20',
      borderRadius: 20,
      padding: 24,
      borderWidth: 1,
      borderColor: '#3D3D3D',
    },
    sectionTitle: {
      fontSize: 'clamp(14px, 2vw, 20px)',
      fontWeight: '600',
      color: '#EFEFEF',
      marginBottom: 10,
    },
    overviewText: {
      color: '#B9B9B9',
      fontSize: 'clamp(8px, 2vw, 14px)',
      lineHeight: 26,
      marginBottom: 20,
      marginTop: 6,
      fontFamily: 'General Sans',
      letterSpacing: 0.4,
    },
    includesSection: {
      backgroundColor: '#34363E',
      padding: 14,
      borderRadius: 10,
    },
    includesTitle: {
      fontSize: 'clamp(14px, 2vw, 18px)',
      fontWeight: '500',
      color: '#EFEFEF',
      marginBottom: 10,
      fontFamily: 'General Sans',
      letterSpacing: '0.36',
    },
    includesText: {
      color: '#B9B9B9',
      fontSize: 'clamp(8px, 2vw, 14px)',
      lineHeight: 18,
      fontFamily: 'General Sans',
      letterSpacing: '0.32',
    },
    pricingContainer: {
      flexDirection: 'row',
      // flexWrap: "wrap",
      justifyContent: 'center',
      alignItems: 'center',

      gap: 16,
    },
    pricingCard: {
      backgroundColor: '#34363E',
      borderRadius: 10,
      padding: 20,
      paddingLeft: 16,
      width: 'clamp(200px, 38vw, 700px)',
    },
    pricingHeader: {
      // backgroundColor: '#34363E',
      // borderRadius: 10,
      // padding: 20,
      // paddingLeft: 16,
      // width: 'clamp(200px, 44vw, 700px)'
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    priceText: {
      fontSize: 'clamp(14px, 2vw, 18px)',
      fontWeight: '400',
      color: '#EFEFEF',
      // marginBottom: 10,
      fontFamily: 'General Sans',
      letterSpacing: '0.4',
      paddingVertical: 'clamp(2px, 1vw, 6px)',
    },

    popularButton: {
      // flexDirection: 'row',
      // alignItems: 'center',
      // alignSelf: 'flex-start',
      marginRight: 8,
      // marginBottom: 4,
      borderWidth: 1,
      backgroundColor: 'transparent',
      borderRadius: 20,
      borderColor: '#EFEFEF',
      paddingVertical: 'clamp(2px, 1vw, 6px)',
      paddingHorizontal: 'clamp(4px, 1vw, 12px)',
    },
    popularText: {
      fontSize: 'clamp(8px, 2vw, 12px)',
      fontWeight: '400',
      color: '#EFEFEF',
      // marginBottom: 10,
      fontFamily: 'General Sans',
      letterSpacing: '0.4',
    },
    capabilitiesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16,
    },
    capabilityCard: {
      backgroundColor: '#34363E',
      borderRadius: 10,
      padding: 20,
      paddingLeft: 16,
      flex: 1,
      minWidth: '48%',
    },
    capabilityHeader: {
      flexDirection: 'row',
      //   alignItems: "flex-start",
      //   marginBottom: 8,
    },
    capabilityIcon: {
      fontSize: 'clamp(12px, 2vw, 16px)',
      marginRight: 16,
    },
    capabilityTitle: {
      fontSize: 'clamp(14px, 2vw, 18px)',
      fontWeight: '500',
      color: '#EFEFEF',
      flex: 1,
      letterSpacing: 0.4,
      marginBottom: 12,
    },
    capabilityDescription: {
      fontSize: 'clamp(8px, 2vw, 14px)',
      color: '#B9B9B9',
      fontWeight: '400',
      fontFamily: 'General Sans',
      lineHeight: 'normal',
      letterSpacing: 0.36,
    },
    permissionsList: {
      //   marginBottom: 8,
    },
    permissionItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginVertical: 12,
    },
    featureList: {
      //   marginBottom: 8,
      maxHeight:'clamp(100px, 20vw, 200px)'
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginVertical: 12,
      width: '20%',
    },
    checkbox: {
      width: 18,
      height: 18,
      borderRadius: 4,
      //   backgroundColor: "#4285f4",
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
      marginLeft:4,
      marginTop: 1,
    },
    checkboxIcon: {
      color: '#EFEFEF',
      fontSize: 'clamp(6px, 2vw, 12px)',
      //   fontWeight: "bold",
    },
    permissionText: {
      color: '#B9B9B9',
      fontSize: 'clamp(8px, 2vw, 14px)',
      flex: 1,
      lineHeight: 'normal',
      letterSpacing: 0.32,
      fontFamily: 'General Sans',
    },
    credentialsNote: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#34363E',
      padding: 12,
      borderRadius: 8,
    },
    quotaNote: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#1A1D20',
      padding: 12,
      borderRadius: 8,
    },
    quotaLabel: {
      color: '#FAFAFA',
      alterFontFamily: 'General Sans',
      fontSize: 'clamp(10px, 2vw, 14px)',
      fontWeight: '300',
      lineHeight: 'normal',
      letterSpacing: 0.32,
      marginRight: 8,
    },
    quotaText: {
      color: '#B9B9B9',
      fontSize: 'clamp(10px, 2vw, 14px)',
      fontWeight: '400',
      flex: 1,
      lineHeight: 'normal',
      fontFamily: 'General Sans',
      letterSpacing: 0.32,
    },
    lockIcon: {
      fontSize: 'clamp(10px, 2vw, 14px)',
      marginRight: 8,
      marginTop: 1,
    },
    credentialsText: {
      color: '#FAFAFA',
      fontSize: 'clamp(10px, 2vw, 14px)',
      fontWeight: '400',
      flex: 1,
      lineHeight: 'normal',
      fontFamily: 'General Sans',
      letterSpacing: 0.32,
    },
    supportLinks: {
      gap: 4,
      flexDirection: 'column',
    },
    supportLink: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 16,
      //   paddingHorizontal: 8,
      borderRadius: 6,
    },
    linkContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    linkIcon: {
      fontSize: 'clamp(10px, 2vw, 14px)',
      marginRight: 12,
    },
    linkTextContainer: {
      flex: 1,
      gap: 4,
      flexDirection: 'row',
    },
    linkTitle: {
      fontSize: 'clamp(8px, 2vw, 14px)',
      fontWeight: '500',
      color: 'white',
      fontFamily: 'General Sans',
      //   marginBottom: 2,
    },
    linkSubtitle: {
      fontSize: 'clamp(8px, 2vw, 14px)',
      color: '#888',
      fontFamily: 'General Sans',
      //   lineHeight: 16,
    },
    arrow: {
      color: '#666',
      fontSize: 'clamp(12px, 2vw, 16px)',
      marginLeft: 2,
    },
    containercta: {
      // marginHorizontal: 16,
      marginVertical: 20,
      backgroundColor: '#1060E0',
      borderRadius: 16,
    },
    gradientContainer: {
      borderRadius: 16,
      // paddingHorizontal: 32,
      paddingVertical: 72,
      alignItems: 'center',
      justifyContent: 'center',
    },
    title: {
      fontSize: 'clamp(38px, 2vw, 42px)',
      fontWeight: '600',
      color: '#EFEFEF',
      textAlign: 'center',
      fontFamily: 'General Sans',
      lineHeight: 70,
      marginBottom: 32,
      // maxWidth: 600,
      letterSpacing: 1.35,
    },
    button: {
      backgroundColor: 'white',
      paddingHorizontal: 32,
      paddingVertical: 16,
      borderRadius: 16,
    },
    activatedButton: {
      backgroundColor: '#23BC79',
      paddingHorizontal: 32,
      paddingVertical: 16,
      borderRadius: 16,
    },
    buttonText: {
      fontSize: 'clamp(20px, 2vw, 24px)',
      fontFamily: 'General Sans',
      fontWeight: '500',
      letterSpacing: 0.52,
      color: '#121212',
      textAlign: 'center',
    },
  });


  export default IntegrationDetail;