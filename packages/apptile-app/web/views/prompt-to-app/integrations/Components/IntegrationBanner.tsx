'use client';

import React from 'react';
import { StyleSheet, View,Text, TouchableOpacity } from 'react-native';

const IntegrationBanner = ({integration, onBannerClick}) => {
  return (
    <View style={styles.containercta}>
    <div
      style={{
        backgroundImage: 'radial-gradient(circle,rgb(172, 171, 171,0.5) 1.4px, transparent 1px)',
        backgroundSize: '24px 24px',
        backgroundPosition: '0 0, 14px 14px',
      }}>
      <View style={styles.gradientContainer}>
        <Text style={styles.title}>
          {integration?.bannerText1} {'\n'} {integration?.bannerText2}
        </Text>
        {integration?.bannerButtonText ? (
          <TouchableOpacity onPress={onBannerClick} style={styles.button} activeOpacity={0.9}>
            <Text style={styles.buttonText}>Activate {integration.bannerButtonText}</Text>
          </TouchableOpacity>
        ) : null}
      </View>
    </div>
  </View>
  );
};

const styles =  StyleSheet.create({
    containercta: {
        // marginHorizontal: 16,
        width:'100%',
        marginVertical: 20,
        backgroundColor: '#1060E0',
        borderRadius: 16,
      },
      gradientContainer: {
        borderRadius: 16,
        // paddingHorizontal: 32,
        paddingVertical: 'clamp(36px, 5vw, 72px)',
        alignItems: 'center',
        justifyContent: 'center',
      },
      title: {
        fontSize: 'clamp(10px, 3vw, 42px)',
        fontWeight: '600',
        color: '#EFEFEF',
        textAlign: 'center',
        fontFamily: 'General Sans',
        lineHeight: 'clamp(2px, 5vw, 70px)',
        marginBottom: 'clamp(16px, 2vw, 32px)',
        // maxWidth: 600,
        letterSpacing: 1.35,
      },
      button: {
        backgroundColor: 'white',
        paddingHorizontal: 'clamp(12px, 3vw, 32px)',
        paddingVertical: 'clamp(6px, 2vw, 16px)',
        borderRadius: 16,
      },
      activatedButton: {
        backgroundColor: '#23BC79',
        paddingHorizontal: 'clamp(12px, 3vw, 32px)',
        paddingVertical: 'clamp(6px, 2vw, 16px)',
        borderRadius: 16,
      },
      buttonText: {
        fontSize: 'clamp(12px, 2vw, 24px)',
        fontFamily: 'General Sans',
        fontWeight: '500',
        letterSpacing: 0.52,
        color: '#121212',
        textAlign: 'center',
      },
})

export default IntegrationBanner;
