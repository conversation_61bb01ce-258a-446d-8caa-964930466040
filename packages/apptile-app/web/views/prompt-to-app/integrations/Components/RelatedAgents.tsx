import { useRef, useState } from "react";
import { StyleSheet, View, TouchableOpacity, Text, ScrollView } from "react-native";
import availableIntegrations from '../availableIntegrations.json'
import TagWithIcon from "./TagWithIcon";
import LogoRender from "./LogoRender";
import {useNavigate, useParams} from '@/root/web/routing.web';

const RelatedAgents = ({integration}) => {
    const navigate = useNavigate();
    const params: any = useParams();
    const integrationCode = params?.integrationCode;
    const [currentIndex, setCurrentIndex] = useState(0);
    const scrollViewRef = useRef<ScrollView>(null);
    const RelatedAgents =availableIntegrations.filter(item=>item.integrationCode !== integrationCode)
  
    const cardWidth = 380;
    const cardMargin = 12;
    const totalCardWidth = cardWidth + cardMargin * 2;
  
    const scrollLeft = () => {
      if (currentIndex > 0) {
        const newIndex = currentIndex - 1;
        setCurrentIndex(newIndex);
        scrollViewRef.current?.scrollTo({
          x: newIndex * totalCardWidth,
          animated: true,
        });
      }
    };
  
    const scrollRight = () => {
      if (currentIndex < availableIntegrations.length - 3) {
        const newIndex = currentIndex + 1;
        setCurrentIndex(newIndex);
        scrollViewRef.current?.scrollTo({
          x: newIndex * totalCardWidth,
          animated: true,
        });
      }
    };
  
    const handleScroll = (event: any) => {
      const scrollX = event.nativeEvent.contentOffset.x;
      const newIndex = Math.round(scrollX / totalCardWidth);
      setCurrentIndex(newIndex);
    };
  
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Related Agents</Text>
  
        <View style={styles.carouselContainer}>
          {/* Left Arrow */}
          <TouchableOpacity
            style={[styles.leftArrow]}
            onPress={scrollLeft}
            disabled={currentIndex === 0}
            activeOpacity={0.7}>
            <Text style={[currentIndex === 0 && {backgroundColor: 'transparent', opacity: 0.3}]}>
              <svg xmlns="http://www.w3.org/2000/svg" width="42" height="42" viewBox="0 0 46 47" fill="none">
                <g clip-path="url(#clip0_1243_1325)">
                  <rect width="27" height="23" transform="matrix(-1 0 0 1 35.9443 12.3516)" fill="#D9D9D9" />
                  <mask id="mask0_1243_1325" maskUnits="userSpaceOnUse" x="0" y="0" width="46" height="47">
                    <path d="M45.4443 0.851562H0.159813V46.1676H45.4443V0.851562Z" fill="white" />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M20.4564 15.8678C20.279 15.72 20.0501 15.6487 19.8202 15.6696C19.5903 15.6905 19.3781 15.8019 19.2302 15.9794L13.4251 22.9511C13.2838 23.1293 13.2177 23.3558 13.241 23.5821C13.2643 23.8084 13.3751 24.0166 13.5498 24.1622C13.7244 24.3079 13.9491 24.3794 14.1757 24.3615C14.4024 24.3436 14.613 24.2378 14.7627 24.0666L20.5678 17.0949C20.7156 16.9173 20.7869 16.6884 20.766 16.4583C20.745 16.2282 20.6337 16.0158 20.4564 15.8678Z"
                      fill="black"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M20.4564 31.1487C20.6337 31.0008 20.745 30.7884 20.766 30.5583C20.7869 30.3282 20.7156 30.0992 20.5678 29.9217L14.7627 22.95C14.613 22.7788 14.4024 22.6729 14.1757 22.655C13.9491 22.6372 13.7244 22.7087 13.5498 22.8543C13.3751 23 13.2643 23.2082 13.241 23.4345C13.2177 23.6608 13.2838 23.8873 13.4251 24.0655L19.2302 31.0372C19.3781 31.2146 19.5903 31.326 19.8202 31.347C20.0501 31.3679 20.279 31.2966 20.4564 31.1487Z"
                      fill="black"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M13.223 23.5082C13.223 23.7393 13.3147 23.961 13.4781 24.1244C13.6414 24.2878 13.8629 24.3796 14.0939 24.3796H31.511C31.7419 24.3796 31.9635 24.2878 32.1268 24.1244C32.2901 23.961 32.3818 23.7393 32.3818 23.5082C32.3818 23.2771 32.2901 23.0554 32.1268 22.892C31.9635 22.7285 31.7419 22.6367 31.511 22.6367H14.0939C13.8629 22.6367 13.6414 22.7285 13.4781 22.892C13.3147 23.0554 13.223 23.2771 13.223 23.5082Z"
                      fill="black"
                    />
                  </mask>
                  <g mask="url(#mask0_1243_1325)">
                    <path
                      d="M22.8018 1.35156C10.5734 1.35173 0.660156 11.272 0.660156 23.5098C0.660262 35.7474 10.5735 45.6678 22.8018 45.668C35.0302 45.668 44.9442 35.7475 44.9443 23.5098C44.9443 11.2719 35.0303 1.35156 22.8018 1.35156Z"
                      fill="black"
                      stroke="#3D3D3D"
                    />
                  </g>
                </g>
                <defs>
                  <clipPath id="clip0_1243_1325">
                    <rect width="45.2845" height="45.316" fill="white" transform="matrix(-1 0 0 1 45.4443 0.851562)" />
                  </clipPath>
                </defs>
              </svg>
            </Text>
          </TouchableOpacity>
  
          {/* Cards ScrollView */}
          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            pagingEnabled={false}
            decelerationRate="fast"
            snapToInterval={totalCardWidth}
            snapToAlignment="start"
            onMomentumScrollEnd={handleScroll}
            contentContainerStyle={styles.scrollContent}
            style={styles.scrollView}>
            {RelatedAgents
              .map((integration, index) => (
                <View key={integration.id} style={styles.card}>
                  <View style={styles.cardHeader}>
                    <View style={styles.logo}>
                     <LogoRender integration={integration} />
                    </View>
                   <View style={styles.cardInfo}>
                      <Text style={styles.cardTitle}>{integration.title}</Text>
                     {integration?.tags && <TagWithIcon key={index} tag={integration.tags[0]} /> } 
                     {integration?.comingSoon && <Text style={styles.comingSoonText}>Coming Soon</Text> } 
                    </View>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      marginTop: 20,
                      justifyContent: 'space-between',
                    }}>
                    <Text style={styles.description}>{integration.excerpt}</Text>
                    <TouchableOpacity onPress={()=>{navigate(`../connect/${integration.integrationCode}`);}} style={styles.learnMoreButton} activeOpacity={0.7}>
                     {!integration?.comingSoon && <Text style={styles.learnMoreText}>Learn more →</Text>}
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
          </ScrollView>
  
          {/* Right Arrow */}
          <TouchableOpacity
            style={[styles.rightArrow]}
            onPress={scrollRight}
            disabled={currentIndex >= availableIntegrations.length - 3}
            activeOpacity={0.7}>
            <Text
              style={[
                currentIndex >= availableIntegrations.length - 3 && {backgroundColor: 'transparent', opacity: 0.3},
              ]}>
              <svg xmlns="http://www.w3.org/2000/svg" width="42" height="42" viewBox="0 0 46 47" fill="none">
                <g clip-path="url(#clip0_1068_4133)">
                  <rect x="10.2158" y="12.3516" width="27" height="23" fill="#D9D9D9" />
                  <mask id="mask0_1068_4133" maskUnits="userSpaceOnUse" x="0" y="0" width="46" height="47">
                    <path d="M0.71582 0.851562H46.0003V46.1676H0.71582V0.851562Z" fill="white" />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M25.7038 15.8678C25.8812 15.72 26.11 15.6487 26.3399 15.6696C26.5699 15.6905 26.7821 15.8019 26.9299 15.9794L32.7351 22.9511C32.8764 23.1293 32.9424 23.3558 32.9191 23.5821C32.8958 23.8084 32.785 24.0166 32.6104 24.1622C32.4357 24.3079 32.2111 24.3794 31.9844 24.3615C31.7578 24.3436 31.5471 24.2378 31.3974 24.0666L25.5923 17.0949C25.4446 16.9173 25.3733 16.6884 25.3942 16.4583C25.4151 16.2282 25.5265 16.0158 25.7038 15.8678Z"
                      fill="black"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M25.7038 31.1487C25.5265 31.0008 25.4151 30.7884 25.3942 30.5583C25.3733 30.3282 25.4446 30.0992 25.5923 29.9217L31.3974 22.95C31.5471 22.7788 31.7578 22.6729 31.9844 22.655C32.2111 22.6372 32.4357 22.7087 32.6104 22.8543C32.785 23 32.8958 23.2082 32.9191 23.4345C32.9424 23.6608 32.8764 23.8873 32.7351 24.0655L26.9299 31.0372C26.7821 31.2146 26.5699 31.326 26.3399 31.347C26.11 31.3679 25.8812 31.2966 25.7038 31.1487Z"
                      fill="black"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M32.9372 23.5082C32.9372 23.7393 32.8454 23.961 32.6821 24.1244C32.5188 24.2878 32.2973 24.3796 32.0663 24.3796H14.6492C14.4182 24.3796 14.1967 24.2878 14.0334 24.1244C13.8701 23.961 13.7783 23.7393 13.7783 23.5082C13.7783 23.2771 13.8701 23.0554 14.0334 22.892C14.1967 22.7285 14.4182 22.6367 14.6492 22.6367H32.0663C32.2973 22.6367 32.5188 22.7285 32.6821 22.892C32.8454 23.0554 32.9372 23.2771 32.9372 23.5082Z"
                      fill="black"
                    />
                  </mask>
                  <g mask="url(#mask0_1068_4133)">
                    <path
                      d="M23.3584 1.35156C35.5868 1.35173 45.5 11.272 45.5 23.5098C45.4999 35.7474 35.5867 45.6678 23.3584 45.668C11.13 45.668 1.21593 35.7475 1.21582 23.5098C1.21582 11.2719 11.1299 1.35156 23.3584 1.35156Z"
                      fill="black"
                      stroke="#3D3D3D"
                    />
                  </g>
                </g>
                <defs>
                  <clipPath id="clip0_1068_4133">
                    <rect width="45.2845" height="45.316" fill="white" transform="translate(0.71582 0.851562)" />
                  </clipPath>
                </defs>
              </svg>
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: 'transparent',
      paddingVertical: 'clamp(8px, 2vw, 24px)',
      //   paddingHorizontal: 16,
      width:'100%'
    },
    title: {
      fontSize: 'clamp(12px, 2vw, 28px)',
      fontWeight: '600',
      letterSpacing: '0.68',
      fontFamily: 'General Sans',
      color: '#FFFFFF',
      marginBottom: 20,
    },
    carouselContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    arrowButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#2a2a2a',
      borderWidth: 1,
      borderColor: '#3a3a3a',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1,
    },
    leftArrow: {
      marginRight: 12,
    },
    rightArrow: {
      marginLeft: 12,
    },
    disabledArrow: {
      backgroundColor: '#1a1a1a',
      borderColor: '#2a2a2a',
    },
    arrowText: {
      fontSize: 20,
      color: '#ccc',
      fontWeight: 'bold',
    },
    disabledArrowText: {
      color: '#555',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingHorizontal: 8,
    },
    card: {
      flex: 1,
      backgroundColor: '#1A1D20',
      borderRadius: 12,
      padding: 12,
      marginRight: 12,
      width: 340,
      justifyContent: 'space-between',
      //   alignContent: 'stretch',
      // aspectRatio: 1 / 0.55,
      borderWidth: 1,
      borderColor: '#3a3a3a',
    },
    cardHeader: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      //   marginBottom: 16,
    },
    logo: {
      marginRight: 10,
    },
    iconStyles: {
      width: 'clamp(40px, 8vw, 75px)',
      height: 'clamp(40px, 8vw, 75px)',
      aspectRatio: 1 / 1,
      borderRadius: 12,
      resizeMode: 'contain',
    },
    logoPlaceholder: {
      width: 'clamp(40px, 4vw, 75px)',
      height: 'clamp(40px, 4vw, 75px)',
      backgroundColor: '#3a3a3a',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#4a4a4a',
    },
    cardInfo: {
      flex: 1,
      marginTop: 8,
      marginBottom: 8,
    },
    titleRow: {
      //   flexDirection: "row",
      //   alignItems: "center",
      //   justifyContent: "space-between",
    },
    cardTitle: {
      fontSize: 16,
      fontWeight: '500',
      color: '#EFEFEF',
      marginBottom: 8,
      lineHeight: 'normal',
      fontFamily: 'General Sans',
      letterSpacing: 0.44,
    },
    tag: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#3a3a3a',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: '#4a4a4a',
    },
    tagIcon: {
      fontSize: 10,
      marginRight: 4,
    },
    tagText: {
      fontSize: 11,
      color: '#ccc',
      fontWeight: '500',
    },
    description: {
      fontSize: 12,
      color: '#B9B9B9',
      lineHeight: 'normal',
      fontWeight: '500',
      marginBottom: 16,
      letterSpacing: '0.32',
      fontFamily: 'General Sans',
    },
    learnMoreButton: {
      alignSelf: 'flex-start',
    },
    learnMoreText: {
      fontSize: 12,
      color: '#EFEFEF',
      fontWeight: '500',
      letterSpacing: 0.36,
      fontFamily: 'General Sans',
      lineHeight: 'normal',
    },
    comingSoonText: {
      fontSize: 12,
      color: '#1060E0',
      fontWeight: '500',
      letterSpacing: 0.36,
      fontFamily: 'General Sans',
      lineHeight: 'normal',
    },
  });

  export default RelatedAgents;