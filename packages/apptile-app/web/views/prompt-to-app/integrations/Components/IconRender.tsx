import { Platform } from 'react-native';
import React from 'react';
import database from '../../../../assets/icons/database.svg';
import security from '../../../../assets/icons/security.svg';
import subscribe from '../../../../assets/icons/light.svg';
import vector from '../../../../assets/icons/vector-square.svg';
import upload from '../../../../assets/icons/upload.svg';
import functions from '../../../../assets/icons/code.svg';
import circleCheck from '../../../../assets/icons/circle-check.svg';
import authentication from '../../../../assets/icons/authentication.svg';
import monetization from '../../../../assets/icons/monetization.svg';
import payment from '../../../../assets/icons/payment.svg';
import Inapppurchase from '../../../../assets/images/Inapppurchase.png';
import Pexels from '../../../../assets/images/pexels.png';
import media from '../../../../assets/icons/media.svg';
import visual from '../../../../assets/icons/visual.svg';

const iconMap: any = {
  database,
  security,
  subscribe,
  vector,
  upload,
  functions,
  circleCheck,
  authentication,
  monetization,
  payment,
  Inapppurchase,
  Pexels,
  media,
  visual
};

const IconRender = ({icon, size = 20}: any) => {
  const src = iconMap[icon];
  return <SVGIcon src={src} size={size} color={true ? '#1060E0' : undefined} />;
};

const SVGIcon: React.FC<{
  src?: string | React.ReactElement;
  size?: number;
  color?: string;
}> = ({src, size = 23, color}) => {
  if (!src) return null;

  // Web environment – we can directly use <img>
  if (Platform.OS === 'web') {
    // If src is a React element (JSX), render it directly
    if (React.isValidElement(src)) {
      return (
        <div
          style={{
            width: size,
            height: size,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {React.cloneElement(src, {
            width: size,
            height: size,
            style: {
              display: 'block',
              flexShrink: 0,
              ...((src as any).props?.style || {}),
            },
          })}
        </div>
      );
    }

    // If src is provided via require('path'), extract path
    const cleanedSrc = (src as string).replace(/^require\(['\"]?/, '').replace(/[\"']?\)$/, '');
    return (
      <div
        style={{
          width: size,
          height: size,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <img
          src={cleanedSrc}
          width={size}
          height={size}
          style={{
            display: 'block',
            flexShrink: 0,
            //   filter: color
            //     ? `brightness(0) saturate(100%) invert(21%) sepia(100%) saturate(1982%) hue-rotate(227deg) brightness(91%) contrast(91%)`
            //     : undefined,
          }}
          alt="icon"
        />
      </div>
    );
  }

  // For native platforms, we'd need a different approach since dynamic requires
  // cause webpack warnings. In practice, SVG icons should be handled through
  // a proper icon library or pre-imported components.
  console.warn('[MenuItem] SVG icons not supported on native platforms with dynamic requires');
  return null;
};

export default IconRender;