import { StyleSheet, View, Text,ScrollView, TouchableOpacity } from "react-native";
import IconRender from "./IconRender";


const IntegrationPricing = ({integration}: any) => {

  const {pricing} = integration
  if(!pricing) return null;
    return (
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
           <View style={styles.card}>
            <Text style={styles.sectionTitle}>Pricing & Limits</Text>
            <Text style={[styles.overviewText]}>{pricing?.tagLine}</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.pricingContainer}>
              {pricing.pricingList.map((item, index) => (
                <View key={`${item.title}-${index}`} style={styles.pricingCard}>
                  <View style={styles.pricingHeader}>
                    <Text style={styles.priceText}>{item?.title}</Text>
                    {item?.isPopular && (
                      <View style={styles.popularButton}>
                        <Text style={styles.popularText}>{item?.popularText}</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.sectionTitle}>{item?.price}</Text>
                  <ScrollView style={styles.featureList} showsVerticalScrollIndicator={false} contentContainerStyle={{justifyContent:'center', alignItems:'flex-start'}}>
                    {item?.features.map((permission, index) => (
                      <View key={index} style={styles.featureItem}>
                        <View style={styles.checkbox}>
                          <Text style={styles.checkboxIcon}>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="20"
                              height="20"
                              viewBox="0 0 24 24"
                              fill="none">
                              <g clip-path="url(#clip0_1068_4064)">
                                <path
                                  d="M6.3998 12.0008L11.1998 16.0008L17.5998 8.00078M11.9998 23.2008C10.529 23.2008 9.0726 22.9111 7.71375 22.3482C6.3549 21.7854 5.12022 20.9604 4.08021 19.9204C3.04019 18.8804 2.21521 17.6457 1.65235 16.2868C1.0895 14.928 0.799805 13.4716 0.799805 12.0008C0.799805 10.53 1.0895 9.07357 1.65235 7.71473C2.21521 6.35588 3.04019 5.1212 4.08021 4.08119C5.12022 3.04117 6.3549 2.21618 7.71375 1.65333C9.0726 1.09048 10.529 0.800781 11.9998 0.800781C14.9702 0.800781 17.819 1.98078 19.9194 4.08119C22.0198 6.18159 23.1998 9.03036 23.1998 12.0008C23.1998 14.9712 22.0198 17.82 19.9194 19.9204C17.819 22.0208 14.9702 23.2008 11.9998 23.2008Z"
                                  stroke="#EFEFEF"
                                  stroke-width="1.6"
                                />
                              </g>
                              <defs>
                                <clipPath id="clip0_1068_4064">
                                  <rect width="24" height="24" fill="white" />
                                </clipPath>
                              </defs>
                            </svg>
                          </Text>
                        </View>
                        <Text style={styles.permissionText}>{permission}</Text>
                      </View>
                    ))}
                  </ScrollView>
                  <View style={styles.quotaNote}>
                    <Text style={styles.quotaLabel}>Exceeds Quota:</Text>
                    <Text style={styles.quotaText}>{item?.quota}</Text>
                  </View>
                </View>
              ))}
            </ScrollView>
          </View>
      </ScrollView>
    );
  };


  const styles = StyleSheet.create({
    container: {
      width: '100%',
      paddingBottom: '2%',
    },
    mainContent: {
      flexDirection: 'row',
      //   padding: 16,
      gap: 24,
    },
    leftColumn: {
      flex: 6,
      gap: 24,
    },
    rightColumn: {
      flex: 4,
      //   maxWidth: 350,
    },
    card: {
      backgroundColor: '#1A1D20',
      borderRadius: 20,
      padding: 24,
      borderWidth: 1,
      borderColor: '#3D3D3D',
    },
    sectionTitle: {
      fontSize: 'clamp(12px, 2vw, 18px)',
      fontWeight: '600',
      color: '#EFEFEF',
      marginBottom: 8,
    },
    overviewText: {
      color: '#B9B9B9',
      fontSize: 'clamp(8px, 2vw, 14px)',
      lineHeight: 26,
      marginBottom: 20,
      marginTop: 6,
      fontFamily: 'General Sans',
      letterSpacing: 0.4,
    },
    includesSection: {
      backgroundColor: '#34363E',
      padding: 14,
      borderRadius: 10,
    },
    includesTitle: {
      fontSize: 'clamp(14px, 2vw, 18px)',
      fontWeight: '500',
      color: '#EFEFEF',
      marginBottom: 10,
      fontFamily: 'General Sans',
      letterSpacing: '0.36',
    },
    includesText: {
      color: '#B9B9B9',
      fontSize: 'clamp(8px, 2vw, 14px)',
      lineHeight: 18,
      fontFamily: 'General Sans',
      letterSpacing: '0.32',
    },
    pricingContainer: {
      flexDirection: 'row',
      // flexWrap: "wrap",
      justifyContent: 'center',
      alignItems: 'center',

      gap: 16,
    },
    pricingCard: {
      backgroundColor: '#34363E',
      borderRadius: 10,
      padding: 20,
      paddingLeft: 16,
      width: 'clamp(200px, 36vw, 700px)',
    },
    pricingHeader: {
      // backgroundColor: '#34363E',
      // borderRadius: 10,
      // padding: 20,
      // paddingLeft: 16,
      // width: 'clamp(200px, 44vw, 700px)'
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 6,
    },
    priceText: {
      fontSize: 'clamp(14px, 2vw, 18px)',
      fontWeight: '400',
      color: '#EFEFEF',
      // marginBottom: 10,
      fontFamily: 'General Sans',
      letterSpacing: '0.4',
      paddingVertical: 'clamp(2px, 1vw, 6px)',
    },

    popularButton: {
      // flexDirection: 'row',
      // alignItems: 'center',
      // alignSelf: 'flex-start',
      marginRight: 8,
      // marginBottom: 4,
      borderWidth: 1,
      backgroundColor: 'transparent',
      borderRadius: 20,
      borderColor: '#EFEFEF',
      paddingVertical: 'clamp(2px, 1vw, 6px)',
      paddingHorizontal: 'clamp(4px, 1vw, 12px)',
    },
    popularText: {
      fontSize: 'clamp(8px, 2vw, 12px)',
      fontWeight: '400',
      color: '#EFEFEF',
      // marginBottom: 10,
      fontFamily: 'General Sans',
      letterSpacing: '0.4',
    },
    capabilitiesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16,
    },
    capabilityCard: {
      backgroundColor: '#34363E',
      borderRadius: 10,
      padding: 20,
      paddingLeft: 16,
      flex: 1,
      minWidth: '48%',
    },
    capabilityHeader: {
      flexDirection: 'row',
      //   alignItems: "flex-start",
      //   marginBottom: 8,
    },
    capabilityIcon: {
      fontSize: 'clamp(12px, 2vw, 16px)',
      marginRight: 16,
    },
    capabilityTitle: {
      fontSize: 'clamp(14px, 2vw, 18px)',
      fontWeight: '500',
      color: '#EFEFEF',
      flex: 1,
      letterSpacing: 0.4,
      marginBottom: 12,
    },
    capabilityDescription: {
      fontSize: 'clamp(8px, 2vw, 14px)',
      color: '#B9B9B9',
      fontWeight: '400',
      fontFamily: 'General Sans',
      lineHeight: 'normal',
      letterSpacing: 0.36,
    },
    permissionsList: {
      //   marginBottom: 8,
    },
    permissionItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginVertical: 12,
    },
    featureList: {
        marginBottom: 8,
      maxHeight:'clamp(100px, 20vw, 100px)'
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginVertical: 8,
      width: '50%',
    },
    checkbox: {
      width: 18,
      height: 18,
      borderRadius: 4,
      //   backgroundColor: "#4285f4",
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
      marginLeft:4,
      marginTop: 1,
    },
    checkboxIcon: {
      color: '#EFEFEF',
      fontSize: 'clamp(6px, 2vw, 12px)',
      //   fontWeight: "bold",
    },
    permissionText: {
      color: '#B9B9B9',
      fontSize: 'clamp(8px, 2vw, 14px)',
      flex: 1,
      lineHeight: 'normal',
      letterSpacing: 0.32,
      fontFamily: 'General Sans',
    },
    credentialsNote: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#34363E',
      padding: 12,
      borderRadius: 8,
    },
    quotaNote: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#1A1D20',
      padding: 12,
      borderRadius: 8,
    },
    quotaLabel: {
      color: '#FAFAFA',
      alterFontFamily: 'General Sans',
      fontSize: 'clamp(10px, 2vw, 14px)',
      fontWeight: '300',
      lineHeight: 'normal',
      letterSpacing: 0.32,
      marginRight: 8,
    },
    quotaText: {
      color: '#B9B9B9',
      fontSize: 'clamp(10px, 2vw, 14px)',
      fontWeight: '400',
      flex: 1,
      lineHeight: 'normal',
      fontFamily: 'General Sans',
      letterSpacing: 0.32,
    },
    lockIcon: {
      fontSize: 'clamp(10px, 2vw, 14px)',
      marginRight: 8,
      marginTop: 1,
    },
    credentialsText: {
      color: '#FAFAFA',
      fontSize: 'clamp(10px, 2vw, 14px)',
      fontWeight: '400',
      flex: 1,
      lineHeight: 'normal',
      fontFamily: 'General Sans',
      letterSpacing: 0.32,
    },
    supportLinks: {
      gap: 4,
      flexDirection: 'column',
    },
    supportLink: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 16,
      //   paddingHorizontal: 8,
      borderRadius: 6,
    },
    linkContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    linkIcon: {
      fontSize: 'clamp(12px, 2vw, 16px)',
      marginRight: 12,
    },
    linkTextContainer: {
      flex: 1,
      gap: 4,
      flexDirection: 'row',
    },
    linkTitle: {
      fontSize: 'clamp(8px, 2vw, 14px)',
      fontWeight: '500',
      color: 'white',
      fontFamily: 'General Sans',
      //   marginBottom: 2,
    },
    linkSubtitle: {
      fontSize: 'clamp(8px, 2vw, 14px)',
      color: '#888',
      fontFamily: 'General Sans',
      //   lineHeight: 16,
    },
    arrow: {
      color: '#666',
      fontSize: 'clamp(12px, 2vw, 16px)',
      marginLeft: 8,
    },
    containercta: {
      // marginHorizontal: 16,
      marginVertical: 20,
      backgroundColor: '#1060E0',
      borderRadius: 16,
    },
    gradientContainer: {
      borderRadius: 16,
      // paddingHorizontal: 32,
      paddingVertical: 72,
      alignItems: 'center',
      justifyContent: 'center',
    },
    title: {
      fontSize: 'clamp(38px, 2vw, 42px)',
      fontWeight: '600',
      color: '#EFEFEF',
      textAlign: 'center',
      fontFamily: 'General Sans',
      lineHeight: 70,
      marginBottom: 32,
      // maxWidth: 600,
      letterSpacing: 1.35,
    },
    button: {
      backgroundColor: 'white',
      paddingHorizontal: 32,
      paddingVertical: 16,
      borderRadius: 16,
    },
    activatedButton: {
      backgroundColor: '#23BC79',
      paddingHorizontal: 32,
      paddingVertical: 16,
      borderRadius: 16,
    },
    buttonText: {
      fontSize: 'clamp(20px, 2vw, 24px)',
      fontFamily: 'General Sans',
      fontWeight: '500',
      letterSpacing: 0.52,
      color: '#121212',
      textAlign: 'center',
    },
  });


  export default IntegrationPricing;