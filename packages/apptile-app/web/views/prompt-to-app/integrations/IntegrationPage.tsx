import {useParams} from '@/root/web/routing.web';
import _, {isNull} from 'lodash';
import React from 'react';
import theme from '../styles-prompt-to-app/theme';
import {StyleSheet, View, Text} from 'react-native';
import availableIntegrations from './availableIntegrations.json';
import LeftSidebar from '../dashboard/LeftSidebar';
import Supabase from './IntegrationsList/Supabase';
import InAppPurchase from './IntegrationsList/InAppPurchase';
import Pexels from './IntegrationsList/Pexels';
import { color } from '@uiw/react-color';

const IntegrationPage = () => {
  const params: any = useParams();
  const integrationCode = params?.integrationCode;
  let integration = !_.isEmpty(integrationCode)
    ? _.find(availableIntegrations, int => int?.integrationCode == integrationCode)
    : _.first(availableIntegrations);

  return (
    <View style={styles.mainContainer}>
      <LeftSidebar mainBar="APP_EDITOR" />
      <View style={styles.containerwrapper}>
        {integrationCode === 'supabase' ? (
          <Supabase integration={integration} />
        ) : integrationCode === 'inAppPurchases' ? (
          <InAppPurchase integration={integration} />
        ) : integrationCode === 'apptile-media-pexels' ? (
          <Pexels integration={integration} />
        ) : <View style={{flex:1, justifyContent:'center', alignItems:'center'}}><Text style={{color:theme.FOREGROUND, fontSize:32}}>Coming Soon</Text></View>}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    minHeight: '100vh',
  },
  containerwrapper: {
    flexDirection: 'column',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#121212',
  }
});

export default IntegrationPage;
