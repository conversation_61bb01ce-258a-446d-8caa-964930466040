[{"id": "1", "title": "Supabase Agent", "excerpt": "Connect your Supabase database to Tile", "icon": "https://cdn.apptile.io/e3a6ad47-c873-4265-8569-5fd53836385f/209b21a2-dfe6-4aa4-aaa5-6dca0172ab5a/original-480x480.jpg", "integrationCode": "supabase", "priceType": "free", "price": "Free", "tags": [{"name": "Database", "iconName": "database", "iconType": "MaterialCommunityIcons"}, {"name": "Authentication", "iconName": "authentication", "iconType": "MaterialCommunityIcons"}], "capabilities": [{"icon": "database", "title": "Instant Postgres", "description": "Create and migrate tables & views directly from the planner agent."}, {"icon": "security", "title": "Auth & Row-Level Security", "description": "Enable email / OAuth sign-in and configure RLS policies inline."}, {"icon": "subscribe", "title": "Realtime subscriptions", "description": "Stream live DB changes to UI components out of the box."}, {"icon": "functions", "title": "Edge Functions", "description": "Generate and deploy TypeScript functions for custom logic or webhooks."}, {"icon": "upload", "title": "Storage buckets", "description": "Upload and serve images / files with signed URLs."}, {"icon": "vector", "title": "Vector embeddings", "description": "Ready for AI features like semantic search."}], "permissions": [{"permission": "Read / write all tables & views in the connected project"}, {"permission": "Manage authentication (users, policies)"}, {"permission": "Upload / fetch objects from Supabase Storage"}, {"permission": "Deploy Edge Functions under your account"}], "pricing": {"tagLine": "Limits are courtesy of Supabase, Tile does not add surcharge", "pricingList": [{"id": 1, "title": "Free", "price": "$0/month", "features": ["500 MB Database", "1 GB Storage", "Unlimited API requests"], "quota": "Upgrade Required", "isPopular": true, "popularText": "Most Popular"}, {"id": 2, "title": "Pro", "price": "$25/month", "features": ["500 MB Database", "1 GB Storage", "Unlimited API requests"], "quota": "Usage-based ($0.125/GB disk)", "isPopular": false, "popularText": "Most Popular"}, {"id": 3, "title": "Premium", "price": "$49/month", "features": ["1 TB Database", "10 GB Storage", "Unlimited API requests", "Unlimited API requests"], "quota": "Usage-based ($0.125/GB disk)", "isPopular": true, "popularText": "Most Popular"}]}, "includes": "Supabase MCP Server, Supabase Developer Agent", "bannerButtonText": "Supabase", "bannerText1": "Ready to power your app with a fully-managed", "bannerText2": "Postgres backend?", "overViewText": "Supabase is the open-source Firebase alternative built on Postgres. With this agent, Tile can automatically start up a Supabase project, generate tables from your data model prompts, scaffold CRUD UI, and wire realtime queries—without you touching SQL or REST"}, {"id": "2", "title": "Tile In-App Purchase", "excerpt": "Connect your Apple App store and Google Play store to Tile", "logo": "Inapppurchase", "icon": "https://cdn.apptile.io/e3a6ad47-c873-4265-8569-5fd53836385f/5b14efe3-eb48-4af3-b593-0e02cdecfe0c/original.png", "integrationCode": "inAppPurchases", "priceType": "free", "price": "Free", "tags": [{"name": "Monetization", "iconName": "monetization", "iconType": "MaterialCommunityIcons"}, {"name": "Payments", "iconName": "payment", "iconType": "MaterialCommunityIcons"}], "capabilities": [{"icon": "subscribe", "title": "Secure Transactions", "description": "Process payments using platform-native billing systems."}, {"icon": "circleCheck", "title": "Product Management", "description": "Define, configure, and manage consumable, non-consumable, and subscription products."}, {"icon": "vector", "title": "Platform Compliance", "description": "Stay compliant with App Store and Google Play purchase policies."}, {"icon": "security", "title": "Entitlements & Access Control", "description": "Control user access to content and features based on their purchase status."}], "pricing": {"tagLine": "Limits are courtesy of In App Purchase, Tile does not add surcharge", "pricingList": [{"id": 1, "title": "Free", "price": "$0/month", "features": ["500 MB Database", "1 GB Storage", "Unlimited API requests"], "quota": "Upgrade Required", "isPopular": true, "popularText": "Most Popular"}, {"id": 2, "title": "Pro", "price": "$25/month", "features": ["500 MB Database", "1 GB Storage", "Unlimited API requests"], "quota": "Usage-based ($0.125/GB disk)", "isPopular": false, "popularText": "Most Popular"}, {"id": 3, "title": "Premium", "price": "$49/month", "features": ["1 TB Database", "10 GB Storage", "Unlimited API requests", "Unlimited API requests"], "quota": "Usage-based ($0.125/GB disk)", "isPopular": true, "popularText": "Most Popular"}]}, "permissions": [{"permission": "Collect payments from users within the app using native In-App Purchase mechanisms"}, {"permission": "Allow you to configure Products and Subscriptions based on the listings created in the App Store and Play Store"}, {"permission": "Generate a dummy preview flow for In-App Purchases that works on both Web and App previews"}], "includes": "In-App purchase DataSource, In-App Purchase Agent", "bannerButtonText": "In-App Purchase", "bannerText1": "Monetize your app effortlessly with", "bannerText2": "native in-app purchases support", "overViewText": "In-App Purchase lets you seamlessly monetize your app with native billing support for iOS and Android. With this agent, Tile can automatically configure your products, handle secure transactions, manage subscriptions, and trigger purchase-based workflows—without you writing any platform-specific code."}, {"id": "3", "title": "<PERSON><PERSON><PERSON>", "excerpt": "Stunning royalty-free photos & videos—automatically woven into your app.", "logo": "<PERSON><PERSON><PERSON>", "icon": "https://cdn.apptile.io/pexels/logo.png", "integrationCode": "apptile-media-pexels", "priceType": "free", "price": "Free", "tags": [{"name": "Media", "iconName": "media", "iconType": "MaterialCommunityIcons"}, {"name": "Visual", "iconName": "visual", "iconType": "MaterialCommunityIcons"}], "capabilities": [{"icon": "upload", "title": "Auto-placeholder media", "description": "Inserts perfectly sized images or short clips wherever your design needs visual polish."}, {"icon": "database", "title": "Sample-data population", "description": "Fills gallery, card, or feed components with realistic media for prototyping and client demos."}, {"icon": "subscribe", "title": "App-store assets", "description": "Curates hero shots, promo banners, and preview videos for Play Store / App Store publishing."}, {"icon": "circleCheck", "title": "Zero friction", "description": "Enabled by default, fully managed by Tile; no user setup or attribution required."}], "permissions": [{"permission": "Perform read-only queries to the Pexels API to fetch public media and metadata"}, {"permission": "Does not access user credentials, personal data, or require write privileges"}], "includes": "Pexels Media Agent", "bannerText1": "Stunning royalty-free photos & videos—", "bannerText2": "automatically woven into your app.", "overViewText": "Pexels is a leading library of high-quality, royalty-free stock photos and videos. With the Pexels Agent baked into Tile, your app instantly gets on-brand imagery—no accounts, keys, or licenses to juggle. The planner agent selects, crops, and compresses assets on the fly, so every screen, sample dataset, and store-listing graphic looks polished from day one."}, {"id": "4", "title": "Google Analytics Agent", "excerpt": "Connect Google Analytics to Tile for insights", "icon": "https://avatars.githubusercontent.com/u/4327788?s=200&v=4", "integrationCode": "google-analytics", "priceType": "coming-soon", "price": "Free", "comingSoon": true}, {"id": "5", "title": "OpenAI Agent", "excerpt": "Connect OpenAI to Tile for AI-powered features", "icon": "https://avatars.githubusercontent.com/u/********?s=200&v=4", "integrationCode": "openai", "priceType": "coming-soon", "price": "Free", "comingSoon": true}, {"id": "6", "title": "Stripe Agent", "excerpt": "Connect your Stripe account to Tile", "icon": "https://avatars.githubusercontent.com/u/856813?s=200&v=4", "integrationCode": "stripe", "priceType": "coming-soon", "price": "Free", "comingSoon": true}, {"id": "7", "title": "Anthropic Agent", "excerpt": "Connect Anthropic's <PERSON> to Tile", "icon": "https://avatars.githubusercontent.com/u/********?s=200&v=4", "integrationCode": "anthropic", "priceType": "coming-soon", "price": "Free", "comingSoon": true}, {"id": "8", "title": "Google Maps Agent", "excerpt": "Integrate Google Maps with Tile", "icon": "https://avatars.githubusercontent.com/u/3717923?s=200&v=4", "integrationCode": "google-maps", "priceType": "coming-soon", "price": "Free", "comingSoon": true}, {"id": "9", "title": "<PERSON><PERSON><PERSON>", "excerpt": "Connect Twilio for communication tools in Tile", "icon": "https://avatars.githubusercontent.com/u/109142?s=200&v=4", "integrationCode": "twi<PERSON>", "priceType": "coming-soon", "price": "Free", "comingSoon": true}, {"id": "10", "title": "Shopify Agent", "excerpt": "Connect your Shopify store to Tile", "icon": "https://avatars.githubusercontent.com/u/8085?s=200&v=4", "integrationCode": "shopify", "priceType": "coming-soon", "price": "Free", "comingSoon": true}, {"id": "11", "title": "n8n Agent", "excerpt": "Connect n8n workflows to Tile for seamless automation", "icon": "https://avatars.githubusercontent.com/u/45487711?s=200&v=4", "integrationCode": "n8n", "priceType": "coming-soon", "price": "Free", "comingSoon": true}]