import TokenApis from '@/root/web/api/TokenApi';
import { MaterialCommunityIcons } from 'apptile-core';
import React, { useCallback, useEffect, useState } from 'react';
import { Pressable, StyleSheet, Text, View, Image } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from '@/root/web/routing.web';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import theme from '../../styles-prompt-to-app/theme';
import RedeemVoucherModal from '../../components/RedeemVoucherModal';
import ModalComponent from '@/root/web/components-v2/base/Modal';


interface UserProfileProps {
  onLogout: () => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ onLogout }) => {
  const params = useParams();
  const dispatch = useDispatch();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showRedeemVoucherModal, setShowRedeemVoucherModal] = useState(false);
  const [tokenUsage, setTokenUsage] = useState<{ used: number; total: number } | null>(null);

  const { user } = useSelector((state: EditorRootState) => state.user);
  const orgId = (params as any).orgId;

  const fetchTokenUsage = useCallback(async () => {
    if (!orgId) return;

    try {
      const response = await TokenApis.getTokenStats(orgId);
      const stats = response.data;
      setTokenUsage({
        used: stats.totalUsed,
        total: stats.totalAllocated,
      });
    } catch (error) {
      console.error('Failed to fetch token usage:', error);
      setTokenUsage(null);
    }
  }, [orgId]);

  useEffect(() => {
    fetchTokenUsage();
  }, [fetchTokenUsage]);

  const handleUpgradePlan = () => {
    console.log('Upgrade plan clicked');
    // TODO: Navigate to upgrade plan
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`;
    }
    if (firstName) {
      return firstName.charAt(0);
    }
    if (lastName) {
      return lastName.charAt(0);
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const fullName = user ? [user.firstname, user.lastname].filter(Boolean).join(' ') : 'User';

  const InitialComponent = () => {
    return (
      <View style={styles.avatarContainer}>
        <Text style={styles.avatarText}>{getInitials(user?.firstname)}</Text>
      </View>
    );
  }


  let progressWidth = (tokenUsage?.used / Math.max(tokenUsage?.total, 1) * 100);
  if ((tokenUsage?.total === 0) || (tokenUsage?.used === 0)) {
    progressWidth = 0;
  }

  if (progressWidth > 100) {
    progressWidth = 100;
  }

  return (
    <>
    <View nativeID="user-profile-root" style={styles.container}>
      <PopoverComponent
        positions={['bottom']}
        containerStyle={{
          // marginLeft: '-13rem',
          // marginTop: '1.2rem',
        }}
        visible={showDropdown}
        onVisibleChange={setShowDropdown}
        trigger={
          <Pressable style={styles.triggerContainer} onPress={() => setShowDropdown(!showDropdown)}>
            <InitialComponent />
            <Text style={styles.userNameText} numberOfLines={1} ellipsizeMode="tail">
              {fullName}
            </Text>
            <MaterialCommunityIcons name="chevron-down" size={24} color="#979797" />
          </Pressable>
        }>
        <View style={styles.dropdownMenu}>
          <View style={{
            paddingHorizontal:20,
            paddingTop:20
          }}>
          <View style={styles.userInfoContainer}>
            <InitialComponent />
            <View style={{
            }}>
              <Text style={styles.userNameText}>{fullName}</Text>
              <Text style={styles.userEmailText}>{user?.email}</Text>
            </View>
          </View>

          <View style={styles.creditsContainer}>
            <View style={styles.creditsInfo}>
              <Text style={styles.creditsText}>
                <Text style={styles.creditsUsed}>
                  {tokenUsage ? (Math.floor(tokenUsage.used / 10000).toFixed(0)) : '0'}
                </Text>
                <Text style={styles.creditsTotal}>
                  /{tokenUsage ? Math.floor(tokenUsage.total / 10000).toFixed(0) : '0'}
                </Text>
                <Text style={styles.creditsLabel}> credits</Text>
              </Text>
              <Pressable onPress={handleUpgradePlan}>
                <Text style={styles.upgradeText}>Upgrade Plan</Text>
              </Pressable>
            </View>
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBarBackground} />
              <View
                style={[
                  styles.progressBarFill,
                  {
                    width: progressWidth
                  },
                ]}
              />
            </View>
          </View>
          </View>

          <View style={styles.separator} />
          <View style={{
                     paddingHorizontal:20,
                     paddingBottom:20
          }}>
            <Pressable style={{ ...styles.menuItem, marginBottom: 22 }} onPress={() => setShowRedeemVoucherModal(true)}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 8V7M8 12.5V11.5M8 17V16M6.8 20H17.2C18.8802 20 19.7202 20 20.362 19.673C20.9265 19.3854 21.3854 18.9265 21.673 18.362C22 17.7202 22 16.8802 22 15.2V8.8C22 7.11984 22 6.27976 21.673 5.63803C21.3854 5.07354 20.9265 4.6146 20.362 4.32698C19.7202 4 18.8802 4 17.2 4H6.8C5.11984 4 4.27976 4 3.63803 4.32698C3.07354 4.6146 2.6146 5.07354 2.32698 5.63803C2 6.27976 2 7.11984 2 8.8V15.2C2 16.8802 2 17.7202 2.32698 18.362C2.6146 18.9265 3.07354 19.3854 3.63803 19.673C4.27976 20 5.11984 20 6.8 20Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>

              <Text style={styles.menuText}>Redeem Voucher</Text>
            </Pressable>
            <Pressable style={styles.menuItem} onPress={onLogout}>
              <Image
                source={require('@/root/web/assets/images/logout_icon.png')}
                style={{ width: 20, height: 20 }}
              />
            <Text style={styles.menuText}>Log out</Text>
          </Pressable>
          </View>
        </View>
      </PopoverComponent>
    </View>
      {
        showRedeemVoucherModal && (
          <>
            <div style={styles.modalOverlay}>
              <ModalComponent
                visible={true}
                onVisibleChange={setShowRedeemVoucherModal}
                content={<RedeemVoucherModal 
                  onRedeem={() => {
                    fetchTokenUsage();
                  }}
                  onClose={() => {
                  setShowRedeemVoucherModal(false)
                }} />}
              />
            </div>
          </>
        )
      }
    </>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: 'transparent',
  },
  container: {
    position: 'relative',
    alignItems: 'center',
  },
  triggerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: theme.CORNER_RADIUS_HALF,
    gap: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.13)',
    backdropFilter: 'blur(4.5px)',
    border: '1px solid rgba(255, 255, 255, 0.10)'
  },
  avatarContainer: {
    width: 42,
    height: 42,
    borderRadius: 10,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    margin:0,
    padding:0
  },
  avatarText: {
    color: '#DEEEFF',
    textAlign: 'center',
    fontSize: 20,
    fontStyle: 'normal',
    fontWeight: '500'
  },
  userNameText: {
    color: '#DEEEFF',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '500',
  },
  userEmailText: {
    color: 'rgba(222, 238, 255, 0.60)',
    fontSize: 13,
    fontStyle: 'normal',
    fontWeight: '400',
  },
  dropdownMenu: {
    width: 320,
    borderWidth: 1,
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    borderColor: 'rgba(255, 255, 255, 0.18)',
    marginTop: '15px',
    marginRight: '35px',
    backgroundColor: 'rgba(255, 255, 255, 0.13)',
    backdropFilter: 'blur(4.5px)',
    border: '1px solid rgba(255, 255, 255, 0.10)'
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  menuText: {
    color: '#DEEEFF',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
    padding:0,
    marginLeft:0,
  },
  separator: {
    height: 1,
    backgroundColor: '#363636',
    marginVertical:20
  },
  creditsContainer: {
    backgroundColor: '#191919',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderColor:'none',
    marginTop:'24px'
  },
  creditsInfo: {
    marginBottom: 12,
  },
  creditsText: {
    fontSize: 16,
    fontFamily: 'Inter',
    lineHeight: 24,
    marginBottom: 8,
  },
  creditsUsed: {
    color: '#CBCBCB',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '700',
  },
  creditsTotal: {
    color: '#6F6F6F',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
  },
  creditsLabel: {
    color: '#D9D9D9',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
  },
  upgradeText: {
    color: '#4581E2',
fontSize: 13,
fontStyle: 'normal',
fontWeight: '400',
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    position: 'relative',
    overflow: 'hidden',
  },
  progressBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#2E2E2E',
  },
  progressBarFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    backgroundColor: '#DEEEFF',
    borderRadius: 2,
  },
});

export default UserProfile;
