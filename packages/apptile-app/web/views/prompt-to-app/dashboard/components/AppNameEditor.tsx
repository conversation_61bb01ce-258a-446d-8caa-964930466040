// import Analytics from '@/root/web/lib/segment';
import AppApi from '@/root/web/api/AppApi';
import TokenApis from '@/root/web/api/TokenApi';
import {MaterialCommunityIcons} from 'apptile-core';
import React, {useEffect, useState, useRef, useCallback} from 'react';
import {Pressable, StyleSheet, Text, View, TextInput, Image} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {useParams, useNavigate} from '@/root/web/routing.web';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
import {makeToast, removeToast} from '@/root/web/actions/toastActions';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import theme from '../../styles-prompt-to-app/theme';
import ModalComponent from '@/root/web/components-v2/base/Modal';
import RedeemVoucherModal from '../../components/RedeemVoucherModal';

const commonStyles = getCommonStyles();

// App Name Editor component with popover menu
const AppNameEditor = () => {
  const [hovered, setHovered] = useState(false);
  const params = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [showRedeemVoucherModal, setShowRedeemVoucherModal] = useState(false);
  const [appName, setAppName] = useState('');
  const [originalName, setOriginalName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [tokenUsage, setTokenUsage] = useState<{used: number; total: number} | null>(null);
  const inputRef = useRef<TextInput>(null);

  // Get app name from Redux store
  const currentApp = useSelector((state: EditorRootState) => state.orgs?.appsById?.[(params as any).id] || {});
  const orgId = (params as any).orgId;

  // Fetch token usage data
  const fetchTokenUsage = useCallback(async () => {
    if (!orgId) return;

    try {
      const response = await TokenApis.getTokenStats(orgId);
      const stats = response.data;
      setTokenUsage({
        used: stats.totalUsed,
        total: stats.totalAllocated,
      });
    } catch (error) {
      console.error('Failed to fetch token usage:', error);
      setTokenUsage(null);
    }
  }, [orgId]);

  useEffect(() => {
    if (currentApp?.name) {
      setAppName(currentApp.name);
      setOriginalName(currentApp.name);
    }
  }, [currentApp?.name]);

  useEffect(() => {
    fetchTokenUsage();
  }, [fetchTokenUsage]);

  // Handle popover menu actions
  const handleGoToDashboard = () => {
    console.log('Go to Dashboard clicked');
    try {
      navigate(`/dashboard/${(params as any).orgId}`);
      console.log('Navigation triggered to:', `/dashboard/${(params as any).orgId}`);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const handleSettings = () => {
    console.log('Settings clicked');
    // TODO: Navigate to settings
  };

  const handleHelp = () => {
    console.log('Help clicked');
    // TODO: Navigate to help
  };

  const handleUpgradePlan = () => {
    console.log('Upgrade plan clicked');
    // TODO: Navigate to upgrade plan
  };

  const handleRenameProject = () => {
    console.log('Rename project clicked');
    setShowDropdown(false);
    setIsEditing(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  const handleEditComplete = async () => {
    if (isSaving) {
      return;
    }

    // If name is empty, revert to original and exit edit mode.
    if (!appName.trim()) {
      setAppName(originalName);
      setIsEditing(false);
      return;
    }

    // If name hasn't changed, just exit edit mode
    if (appName === originalName) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);

    // Show progress toast with unique ID
    const progressToastId = 'rename-progress-' + Date.now();
    dispatch(
      makeToast({
        id: progressToastId,
        content: 'Renaming app...',
        appearances: 'info',
      }),
    );

    try {
      await AppApi.updateBasicAppInfo((params as any).id, {name: appName});
      setOriginalName(appName);

      // Remove progress toast and show success
      dispatch(removeToast(progressToastId));
      dispatch(
        makeToast({
          content: 'App name updated successfully',
          appearances: 'success',
        }),
      );

      // Track analytics event
      // Analytics.track('editor:app_name_updated');

      // Refresh app data
      dispatch({
        type: 'FETCH_ORGS',
      });
    } catch (error) {
      console.error('Failed to update app name:', error);
      setAppName(originalName);

      // Remove progress toast and show error
      dispatch(removeToast(progressToastId));
      dispatch(
        makeToast({
          content: 'Failed to update app name',
          appearances: 'error',
        }),
      );
    } finally {
      setIsSaving(false);
      setIsEditing(false);
    }
  };

  let progressWidth = (tokenUsage?.used / Math.max(tokenUsage?.total, 1) * 100);
  if ((tokenUsage?.total === 0) || (tokenUsage?.used === 0)) {
    progressWidth = 0;
  }

  if (progressWidth > 100) {
    progressWidth = 100;
  }

  return (
    <>
    <View 
      nativeID="appname-editor-root" 
      style={[styles.integratedContainer, {backgroundColor: hovered ? theme.HOVER_COLOR : theme.BACKGROUND}]} 
    >
      {/* Combined app name and dropdown container with Figma specs */}
      <PopoverComponent
        positions={['bottom', 'top', 'left', 'right']}
        containerStyle={
          {
            // marginLeft: '-13rem',
            // marginTop: '1.2rem',
          }
        }
        visible={showDropdown}
        onVisibleChange={setShowDropdown}
        trigger={
          <Pressable
            onHoverIn={() => {
              setHovered(true);
            }}
            onHoverOut={() => {
              setHovered(false);
            }}
            style={styles.appNameDropdownContainer}
            onPress={() => {
              if (!isEditing) {
                setShowDropdown(!showDropdown);
              }
            }}>
            {/* Logo section */}
            <View style={styles.logoContainer}>
              <Image
                style={styles.logo}
                source={require('@/root/web/assets/images/logoSlim.svg')}
                resizeMode="contain"
              />
            </View>

            {/* App name section */}
            <View style={styles.appNameSection}>
              {isEditing ? (
                <TextInput
                  ref={inputRef}
                  style={[commonStyles.baseText, styles.appNameInput]}
                  value={appName}
                  onChangeText={setAppName}
                  onBlur={handleEditComplete}
                  onSubmitEditing={handleEditComplete}
                  maxLength={32}
                  selectTextOnFocus
                  editable={!isSaving}
                />
              ) : (
                <View style={styles.appNameDisplayWrapper}>
                  <Text style={[commonStyles.baseText, styles.appNameText]} numberOfLines={1} ellipsizeMode="tail">
                    {appName || 'Untitled App'}
                  </Text>
                </View>
              )}
            </View>

            {/* Dropdown icon */}
            <View style={styles.dropdownContainer}>
              <MaterialCommunityIcons name="chevron-down" size={24} color="#979797" />
            </View>
          </Pressable>
        }>
        <View style={styles.userDropdownMenu}>
          <Pressable style={styles.userMenuItem} onPress={handleGoToDashboard}>
            <MaterialCommunityIcons name="chevron-left" size={20} color="#DEEEFF" />
            <Text style={styles.userMenuText}>Go to Dashboard</Text>
          </Pressable>

          <View style={styles.userSeparator} />

          <Pressable style={{ ...styles.userMenuItem, marginBottom: 0 }} onPress={() => setShowRedeemVoucherModal(true)}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 8V7M8 12.5V11.5M8 17V16M6.8 20H17.2C18.8802 20 19.7202 20 20.362 19.673C20.9265 19.3854 21.3854 18.9265 21.673 18.362C22 17.7202 22 16.8802 22 15.2V8.8C22 7.11984 22 6.27976 21.673 5.63803C21.3854 5.07354 20.9265 4.6146 20.362 4.32698C19.7202 4 18.8802 4 17.2 4H6.8C5.11984 4 4.27976 4 3.63803 4.32698C3.07354 4.6146 2.6146 5.07354 2.32698 5.63803C2 6.27976 2 7.11984 2 8.8V15.2C2 16.8802 2 17.7202 2.32698 18.362C2.6146 18.9265 3.07354 19.3854 3.63803 19.673C4.27976 20 5.11984 20 6.8 20Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <Text style={styles.userMenuText}>Redeem Voucher</Text>
          </Pressable>


          <Pressable style={styles.userMenuItem} onPress={handleRenameProject}>
            <MaterialCommunityIcons name="pencil" size={20} color="#DEEEFF" />
            <Text style={styles.userMenuText}>Rename Project</Text>
          </Pressable>

          <View style={styles.userCreditsContainer}>
            <View style={styles.userCreditsInfo}>
              <Text style={styles.userCreditsText}>
                <Text style={styles.userCreditsUsed}>
                {tokenUsage ? (Math.floor(tokenUsage.used / 10000).toFixed(0)) : '0'}
                </Text>
                <Text style={styles.userCreditsTotal}>
                /{tokenUsage ? Math.floor(tokenUsage.total / 10000).toFixed(0) : '0'}
                </Text>
                <Text style={styles.userCreditsLabel}> credits</Text>
              </Text>
              <Pressable onPress={handleUpgradePlan}>
                <Text style={styles.userUpgradeText}>Upgrade Plan</Text>
              </Pressable>
            </View>
            <View style={styles.userProgressBarContainer}>
              <View style={styles.userProgressBarBackground} />
              <View
                style={[
                  styles.userProgressBarFill,
                  {
                    width:progressWidth
                  },
                ]}
              />
            </View>
          </View>

          <View style={styles.userSeparator} />

          <Pressable style={styles.userMenuItem} onPress={handleHelp}>
            <MaterialCommunityIcons name="help-circle-outline" size={20} color="#DEEEFF" />
            <Text style={styles.userMenuText}>Help</Text>
          </Pressable>
        </View>
      </PopoverComponent>
    </View>
    {
        showRedeemVoucherModal && (
          <>
            <div style={styles.modalOverlay}>
              <ModalComponent
                visible={true}
                onVisibleChange={setShowRedeemVoucherModal}
                content={<RedeemVoucherModal 
                  onRedeem={() => {
                    fetchTokenUsage();
                  }}
                  onClose={() => {
                  setShowRedeemVoucherModal(false)
                }} />}
              />
            </div>
          </>
        )
      }
    </>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: 'transparent',
  },
  // Integrated container matching Figma frame (380x52px)
  integratedContainer: {
    width: '100%',
    height: 52,
    borderRadius: theme.CORNER_RADIUS_HALF,
    position: 'relative',
    marginBottom: theme.THIN_GAP,
    flexDirection: 'row',
    alignItems: 'center',
    // paddingHorizontal: theme.PADDING,
  },
  // Logo container
  logoContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  logo: {
    width: '100%',
    height: '100%',
  },
  // Combined app name and dropdown container with Figma specifications
  appNameDropdownContainer: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    gap: 8,
    padding: theme.PADDING,
  },
  // App name section
  appNameSection: {
    flex: 1,
    minWidth: 0, // Allow flex shrinking for text truncation
  },
  // App name display wrapper (no dropdown icon here anymore)
  appNameDisplayWrapper: {
    justifyContent: 'center',
    height: 32,
  },
  appNameText: {
    color: '#FAFAFA', // Figma text color
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'General Sans',
    opacity: 0.9, // Slightly higher opacity for better readability
  },
  // Dropdown container positioned at the end
  dropdownContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownTrigger: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  // User dropdown styles matching LeftSidebar glassmorphism
  userDropdownMenu: {
    width: 384,
    borderWidth: 1,
    alignSelf: 'center',
    padding: 0,
    overflow: 'hidden',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    borderColor: 'rgba(255, 255, 255, 0.18)',
    backgroundColor: 'rgba(255, 255, 255, 0.04)',
    backdropFilter: 'blur(10.5px)',
  },
  userMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 12,
  },
  userMenuText: {
    color: '#DEEEFF',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'General Sans',
    fontStyle: 'normal',
  },
  userSeparator: {
    height: 1,
    backgroundColor: '#363636',
    marginHorizontal: 0,
  },
  userCreditsContainer: {
    backgroundColor: '#363636',
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#4E4E4E',
  },
  userCreditsInfo: {
    marginBottom: 12,
  },
  userCreditsText: {
    fontSize: 18,
    fontFamily: 'Inter',
    lineHeight: 28,
    marginBottom: 8,
  },
  userCreditsUsed: {
    color: '#DEEEFF',
    fontWeight: 'bold',
  },
  userCreditsTotal: {
    color: '#6F6F6F',
    fontWeight: 'normal',
  },
  userCreditsLabel: {
    color: '#DEEEFF',
    fontWeight: 'normal',
  },
  userUpgradeText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Inter',
  },
  userProgressBarContainer: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    position: 'relative',
    overflow: 'hidden',
  },
  userProgressBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#2E2E2E',
  },
  userProgressBarFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    backgroundColor: '#DEEEFF',
    borderRadius: 2,
  },
  appNameInput: {
    color: '#FAFAFA',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'DM Sans',
    height: 32,
    paddingHorizontal: 8,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 4,
    outline: 'none',
  },
});

export default AppNameEditor;
