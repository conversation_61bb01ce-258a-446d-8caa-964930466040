import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Text, Pressable, Image} from 'react-native';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {useMatch, useNavigate, useParams} from 'react-router';

import {LeftPane as NotificationLeftPaneContent} from '@/root/web/views/notificationAdmin/shared/leftPane';
import MenuItem from '../../../components-v2/base/Menu';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import {useIsPreview} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import {handleLogout} from '@/root/web/views/auth/authUtils';
import {softRestartConfig, toggleChatView} from '../../../actions/editorActions';
import {selectMandatoryCheck} from '../../../selectors/EditorModuleSelectors';
import theme from '../styles-prompt-to-app/theme';
import {navigationTreeComponent} from '../components/CreateNavTreeComponent';
import {AnalyticsP2A, BuildP2A, DashboardP2A, EditP2A, IntegrationsP2A, NotificationsP2A} from '../editor/components/svgelems';

const styles = StyleSheet.create({
  sidebarLeft: {
    height: '100%',
    overflow: 'hidden',
    pointerEvents: 'auto',
    backgroundColor: theme.BACKGROUND,
  },
  sidebarHeader: {
    height: '8vh',
    minHeight: 'calc(2vh + 48px)',
    paddingVertical: '1vh',
    flexDirection: 'row',
    marginBottom: 10,
  },
  logo: {
    width: 120,
    height: 36,
    marginHorizontal: 9,
    marginVertical: 11,
  },
  logoSmall: {
    width: 36,
    height: 36,
    marginHorizontal: 9,
    marginVertical: 11,
  },
  sidebarLeftSecondary: {
    position: 'absolute',
    top: 0,
    left: 80,
    bottom: 0,
    flex: 1,
    height: '100%',
    backgroundColor: theme.BACKGROUND,
    overflow: 'hidden',
    zIndex: -1,
  },
  iconPressable: {
    width: 32,
    height: 32,
    borderRadius: theme.CORNER_RADIUS_HALF,
    // borderWidth: 1,
    // borderColor: 'red',
    alignItems: 'center',
    justifyContent: 'center',
  },
  sidebarContentContainer: {
    flex: 1,
    overflow: 'scroll',
  },
  leftSidebarWrapper: {width: 270},
  toTilesWrapper: {
    position: 'absolute',
    left: 80,
    top: 0,
    zIndex: -1,
    backgroundColor: '#F8FBF8',
    paddingHorizontal: 30,
    borderRadius: 21,
    height: 200,
    justifyContent: 'center',
    shadowColor: 'rgba(99, 99, 99, 0.45)',
    shadowOffset: {width: 0, height: 0},
    shadowRadius: 40,
  },
  tooltipMenuWrapper: {
    position: 'absolute',
    left: 0,
    top: -17,
    backgroundColor: '#000000',
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    zIndex: 9,
    height: 25,
  },
  leftEditorSidebarText: {
    fontSize: 12,
    color: theme.FOREGROUND,
  },
  loaderImage: {
    width: 60,
    height: 60,
  },
  userAvatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#63ADFE',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 0,
  },
  userAvatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  userDropdownMenu: {
    width: 250, // Smaller width than app name editor
    borderWidth: 1,
    alignSelf: 'center',
    padding: 0,
    overflow: 'hidden',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    borderColor: 'rgba(255, 255, 255, 0.18)',
    backgroundColor: 'rgba(30, 30, 30, 0.75)', // Darker glassmorphism
    backdropFilter: 'blur(10.5px)',
    position: 'absolute',
    bottom: '100%',
    left: '100%',
    marginLeft: 10,
    marginBottom: 10,
  },
  userPopupContainer: {
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.13)',
    width: 277,
    height: 124,
    marginLeft: 35,
    backdropFilter: 'blur(7.5px)',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  userAvatarContainer_popover: {
    width: 40,
    height: 40,
    borderRadius: 8, // Squircle shape
    backgroundColor: '#007AFF', // Blue background
    alignItems: 'center',
    justifyContent: 'center',
  },
  userAvatarText_popover: {
    color: 'white',
    fontSize: 20,
    fontWeight: '600',
  },
  userNameText: {
    color: '#DEEEFF',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '500',
  },
  userEmailText: {
    color: 'rgba(222, 238, 255, 0.60)',
    fontSize: 13,
    fontStyle: 'normal',
    fontWeight: '400',
    marginTop: 4,
  },
  userSeparator: {
    height: 2,
    backgroundColor: '#7B7B7B',
  },
  userMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  userMenuText: {
    color: '#DEEEFF',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
  },
});

type LeftSidebarProps = {
  mainBar?: string;
  secondaryBar?: string;
  customMainBar?: any;
};

const LeftSidebar: React.FC<LeftSidebarProps> = props => {
  const {mainBar: currentMainBar, secondaryBar: currentSecondaryBar, customMainBar: CustomMainBar} = props;

  const [mainBar, setMainBar] = React.useState(currentMainBar ?? '');
  const [secondaryBar, setSecondaryBar] = React.useState<string | boolean>(true);

  const isPreview = false;
  const [secondarySidebarZIndex, setSecondarySidebarZIndex] = useState(false);
  const sidebarLeftSecondaryTranslateX = useSharedValue(-450);

  useEffect(() => {
    !secondaryBar && setSecondarySidebarZIndex(false);
    // Secondary sidebar should only show when secondary content is requested
    const shouldShowSecondary = secondaryBar && !isPreview;
    sidebarLeftSecondaryTranslateX.value = withTiming(
      shouldShowSecondary ? 0 : -450,
      {
        duration: 300, // Match main sidebar animation speed
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      },
      () => {
        shouldShowSecondary && setSecondarySidebarZIndex(true);
      },
    );
  }, [isPreview, secondaryBar, sidebarLeftSecondaryTranslateX]);
  const sidebarLeftSecondaryAnimation = useAnimatedStyle(() => ({
    transform: [{translateX: sidebarLeftSecondaryTranslateX.value}],
    zIndex: secondarySidebarZIndex ? 9 : -1,
  }));

  let leftpane = (
    <LeftPaneContent
      mainBar={mainBar}
      setMainBar={setMainBar}
      secondaryBar={secondaryBar}
      setSecondaryBar={setSecondaryBar}
    />
  );

  return (
    <View nativeID="thin-sidebar-root" style={[secondaryBar ? {zIndex: 2} : {}, {width: 60}]}>
      <Animated.View style={styles.sidebarLeft}>{leftpane}</Animated.View>

      <Animated.View
        style={[
          // styles.leftSidebarWrapper,
          styles.sidebarLeftSecondary,
          sidebarLeftSecondaryAnimation,
          {overflow: 'visible'},
        ]}>
        {secondaryBar === 'pages' && (
          <View
            style={[
              styles.sidebarContentContainer,
              {
                width: '300px',
              },
            ]}>
            {React.createElement(navigationTreeComponent())}
          </View>
        )}
      </Animated.View>
    </View>
  );
};

type LeftPaneContentProps = {
  mainBar: string;
  setMainBar: (val: string) => void;
  secondaryBar: string | boolean;
  setSecondaryBar: (val: string | boolean) => void;
};

const LeftPaneContent: React.FC<LeftPaneContentProps> = props => {
  const {mainBar} = props;

  switch (mainBar) {
    case 'DASHBOARD':
      return <DashBoardLeftPaneContent {...props} />;
    case 'MANUAL_NOTIFICATION_PLAYGROUND':
      return <NotificationLeftPaneContent />;
    case 'AUTOMATED_NOTIFICATION_PLAYGROUND':
      return <NotificationLeftPaneContent isAutomatedCampaign />;
    case 'APP_EDITOR':
      return <AppEditorLeftPaneContent {...props} />;
    default:
      return <></>;
  }
};

type DashBoardLeftPaneContentProps = {} & LeftPaneContentProps;

const DashBoardLeftPaneContent: React.FC<DashBoardLeftPaneContentProps> = props => {
  // Simple placeholder for dashboard content
  return (
    <View style={[styles.sidebarContentContainer, {overflow: 'hidden'}]}>
      <MenuItem
        id={'dashboard-placeholder'}
        text={'Dashboard'}
        icon={'home'}
        iconType="MaterialCommunityIcons"
        isActive={false}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          // Dashboard navigation logic
        }}
      />
    </View>
  );
};

type AppEditorLeftPaneContentProps = {} & LeftPaneContentProps;

export const AppEditorLeftPaneContent: React.FC<AppEditorLeftPaneContentProps> = props => {
  const {secondaryBar, setSecondaryBar} = props;
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {user} = useSelector((state: EditorRootState) => state.user);
  const userEmail = user?.email;
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const params = useParams();

  // Track the current active item in the sidebar based on current path
  const [activeItem, setActiveItem] = React.useState<string>('');
  const [hoveredItem, setHoveredItem] = React.useState('');

  // Set the active item based on the current route and sidebar state
  useEffect(() => {
    // Check if we're on the integrations page
    if (window.location.pathname.includes('/integrations')) {
      setActiveItem('integrations');
    } else if (window.location.pathname.includes('/editor')) {
      setActiveItem('editor');
    } else if (window.location.pathname.includes('/notifications')) {
      setActiveItem('notifications');
    } else if (window.location.pathname.includes('/analytics')) {
      setActiveItem('analytics');
    } else if (window.location.pathname.includes('/publish')) {
      setActiveItem('builds');
    }

    // For the Pages tab, we need to track it based on the secondaryBar state
    // since it doesn't change the URL but opens a sidebar
    if (secondaryBar === 'pages') {
      setActiveItem('pages');
    }
  }, [params, secondaryBar, window.location.pathname]);

  const mandatoryFields = useSelector(selectMandatoryCheck());

  const toggleChat = () => {
    dispatch(toggleChatView());
  };

  const onSoftRefreshEditor = () => {
    dispatch(softRestartConfig());
  };

  // User initial for avatar
  const [userInitial, setUserInitial] = React.useState<string>('');

  useEffect(() => {
    if (userEmail && typeof userEmail === 'string') {
      setUserInitial(userEmail.charAt(0).toUpperCase());
    }
  }, [userEmail]);

  const onLogout = async () => {
    if (isLoggingOut) return;

    setShowUserMenu(false);

    await handleLogout({
      redirectUrl: '/',
      setIsLoggingOut,
      onSuccess: () => console.log('Logout successful'),
    });
  };

  return (
    <View
      style={[
        styles.sidebarContentContainer,
        {
          overflow: 'hidden',
          flex: 1,
          justifyContent: 'space-between',
        },
      ]}>
      <View style={{alignItems: 'center', paddingTop: 88, gap: '40px'}}>
        <Pressable
          style={[
            styles.iconPressable,
            {
              backgroundColor:
                activeItem === 'editor'
                  ? theme.ACCENT
                  : hoveredItem === 'editor'
                  ? theme.HOVER_COLOR
                  : theme.BACKGROUND,
            },
          ]}
          onHoverIn={() => {
            setHoveredItem('editor');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          onPress={() => {
            if (activeItem !== 'editor' && secondaryBar !== 'pages') {
              // window.location.href = `/dashboard/${(params as any)?.orgId}/app/${(params as any)?.id}`;
              navigate(`/dashboard/${(params as any)?.orgId}/app/${(params as any)?.id}/f/${(params as any)?.forkId}/b/${(params as any)?.branchName}/dashboard`);
            } else {
              setActiveItem('editor');
              setSecondaryBar('editor');
            }
          }}>
          <EditP2A
            size={activeItem === 'editor' ? 20 : 24}
            fill={activeItem === 'editor' ? theme.FOREGROUND : theme.FOREGROUND_OP_HALF}
          />
        </Pressable>

        <Pressable
          style={[
            styles.iconPressable,
            {
              backgroundColor:
                activeItem === 'integrations'
                  ? theme.ACCENT
                  : hoveredItem === 'integrations'
                  ? theme.HOVER_COLOR
                  : theme.BACKGROUND,
            },
          ]}
          onHoverIn={() => {
            setHoveredItem('integrations');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          onPress={() => {
            setActiveItem('integrations');
            navigate(
              `/dashboard/${(params as any)?.orgId}/app/${(params as any)?.id}/f/${(params as any)?.forkId}/b/${
                (params as any)?.branchName
              }/dashboard/integrations`,
            );
          }}>
          <IntegrationsP2A
            size={activeItem === 'integrations' ? 20 : 24}
            fill={activeItem === 'integrations' ? theme.FOREGROUND : theme.FOREGROUND_OP_HALF}
          />
        </Pressable>
        <Pressable
          style={[
            styles.iconPressable,
            {
              backgroundColor:
                activeItem === 'notifications'
                  ? theme.ACCENT
                  : hoveredItem === 'notifications'
                  ? theme.HOVER_COLOR
                  : theme.BACKGROUND,
            },
          ]}
          onHoverIn={() => {
            setHoveredItem('notifications');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          onPress={() => {
            setActiveItem('notifications');
            navigate(
              `/dashboard/${(params as any)?.orgId}/app/${(params as any)?.id}/f/${(params as any)?.forkId}/b/${
                (params as any)?.branchName
              }/dashboard/notifications`,
            );
          }}>
          <NotificationsP2A
            size={activeItem === 'notifications' ? 20 : 24}
            fill={activeItem === 'notifications' ? theme.FOREGROUND : theme.FOREGROUND_OP_HALF}
          />
        </Pressable>

        <Pressable
          style={[
            styles.iconPressable,
            {
              backgroundColor:
                activeItem === 'analytics'
                  ? theme.ACCENT
                  : hoveredItem === 'analytics'
                  ? theme.HOVER_COLOR
                  : theme.BACKGROUND,
            },
          ]}
          onHoverIn={() => {
            setHoveredItem('analytics');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          onPress={() => {
            setActiveItem('analytics');
            navigate(
              `/dashboard/${(params as any)?.orgId}/app/${(params as any)?.id}/f/${(params as any)?.forkId}/b/${
                (params as any)?.branchName
              }/dashboard/analytics`,
            );
          }}>
          <AnalyticsP2A
            size={activeItem === 'analytics' ? 20 : 24}
            fill={activeItem === 'analytics' ? theme.FOREGROUND : theme.FOREGROUND_OP_HALF}
          />
        </Pressable>
        <Pressable
          style={[
            styles.iconPressable,
            {
              backgroundColor:
                activeItem === 'builds'
                  ? theme.ACCENT
                  : hoveredItem === 'builds'
                  ? theme.HOVER_COLOR
                  : theme.BACKGROUND,
            },
          ]}
          onHoverIn={() => {
            setHoveredItem('builds');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          onPress={() => {
            setActiveItem('builds');
            navigate(
              `/dashboard/${(params as any)?.orgId}/app/${(params as any)?.id}/f/${(params as any)?.forkId}/b/${
                (params as any)?.branchName
              }/publish/dashboard/publish`,
            );
          }}>
          <BuildP2A
            size={activeItem === 'builds' ? 20 : 24}
            fill={activeItem === 'builds' ? theme.FOREGROUND : theme.FOREGROUND_OP_HALF}
          />
        </Pressable>
      </View>
      <View style={{alignItems: 'center', paddingBottom: 24}}>
        <PopoverComponent
          visible={showUserMenu}
          onVisibleChange={setShowUserMenu}
          positions={['top']}
          contentStyle={styles.userDropdownMenu}
          trigger={
            <Pressable 
              onPress={() => setShowUserMenu(prev => !prev)}
              onHoverIn={() => setHoveredItem("userinitial")}
              onHoverOut={() => setHoveredItem("")}
            >
              <View style={[styles.userAvatarContainer, {
                backgroundColor: hoveredItem === "userinitial" ?  "#3B98FE" : "#007AFF"
              }]}>
                <Text style={styles.userAvatarText}>{userInitial || ''}</Text>
              </View>
            </Pressable>
          }>
          <View style={styles.userPopupContainer}>
            <View style={styles.userInfoContainer}>
              <View style={styles.userAvatarContainer_popover}>
                <Text style={styles.userAvatarText_popover}>{userInitial || ''}</Text>
              </View>
              <View style={{marginLeft: 12}}>
                <Text style={styles.userNameText}>{user?.firstname}</Text>
                <Text style={styles.userEmailText}>{userEmail}</Text>
              </View>
            </View>
            <View style={styles.userSeparator} />
            <Pressable style={styles.userMenuItem} onPress={onLogout}>
              <Image
                source={require('@/root/web/assets/images/logout_icon.png')}
                style={{ width: 20, height: 20 }}
              />
              <Text style={[styles.userMenuText, { marginLeft: 12 }]}>Log out</Text>
            </Pressable>
          </View>
        </PopoverComponent>
      </View>
    </View>
  );
};

export default LeftSidebar;
