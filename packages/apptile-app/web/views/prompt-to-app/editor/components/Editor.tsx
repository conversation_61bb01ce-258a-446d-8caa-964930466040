import React, {Ref, useCallback, useEffect, useRef, useState} from 'react';
import Animated, {useAnimatedStyle, useSharedValue, withTiming, Easing} from 'react-native-reanimated';
import {Modal, StyleSheet, View, useWindowDimensions, Text, Pressable} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';

import {fetchAppBranchesWithScheduledOta, openChatView, toggleChatView} from '@/root/web/actions/editorActions';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import BetaBuildTag from '@/root/web/components/BetaBuildTag';
import {cloneOpenApp} from '@/root/web/components/pluginServer';
import WebSDKBetaBuildTag from '@/root/web/components/WebSDKBetaBuildTag';
import EditorContext from '@/root/web/context/editorContext';
import {PlatformState} from '@/root/web/store/PlatformReducer';
// import theme from '@/root/web/styles-v2/theme';
import {
  ApptileCanvasScaleContext,
  initApptileIsEditable,
  store,
  ThemeContainer,
  useCallbackRef,
  useIsEditable,
  DispatchActions,
  selectAppConfig,
  useIsPreview,
} from 'apptile-core';
import {chat, edit, borderColor, borderDots, plus} from './svgelems';
import {Resizable} from 're-resizable';
import {HotKeys} from 'react-hotkeys';
import ApptileApp from '../../../../../app/ApptileApp';
import {HOTKEY_MAPS} from '../../../../common/hotKeyMaps';
import {EditorState} from '../../../../common/webDatatypes';
import {CustomDragLayer} from '../../../../components/CustomDragLayer';
import {useParams} from '../../../../routing.web';
import LeftSidebar from '../../dashboard/LeftSidebar';
import RightSidebar from '@/root/web/layout-v2/RightSidebar';
// import {AIClientV2} from '@/root/web/components/codeEditor/aiClientV2';
import {AIClient} from '@/root/web/components/codeEditor/aiClient';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';
import theme from '../../styles-prompt-to-app/theme';
import {TopBar} from '../../dashboard/TopBar';
import {selectSelectedPluginConfig} from '@/root/web/selectors/EditorSelectors';
import {reloadExternalPlugins} from '@/root/app/plugins/initPlugins';
import {useNavigate} from '../../../../routing.web';
import AppNameEditor from '../../dashboard/components/AppNameEditor';
import {TopRightControls} from '../../dashboard/components/TopRightControls';
import {ScreenSelector} from '../../dashboard/components/ScreenSelector';
import PreviewModule from '@/root/web/layout-v2/PreviewModule';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import InlineChatDialog from '../../components/InlineChatDialog';
import GradientLoader from '@/root/web/components/GradientLoader';
import RecheckModal from './RecheckModal';

interface EditorProps {
  editor: EditorState;
  platform: PlatformState;
  changeAppConfig: (appId: string, orgId: string, forkId: string | number, branchName?: string) => void;
  orgs: any;
  fetchOrgs: () => void;
  saveAppState: (
    newSave: boolean,
    showToast: boolean,
    message?: string,
    backup?: boolean,
    force?: boolean,
    saveWithCacheWithoutPublishing?: boolean,
  ) => void;
  updateApp: (publishedAppSaveId: number) => void;
  editorCopyAction: () => void;
  editorPasteAction: (pasteString: string) => void;
  configureDatasources: (appId: string, forceUpdateSecrets: boolean) => void;
  clearDatasourcesCredentials: () => void;
  softRestartConfig: () => void;
  changeAppContextData: (appId: string) => void;
  initApptileTilesMode: (isTilesOnly: boolean) => void;
  params: Record<string, string>;
}

export const CANVAS_SCALE = 0.8;
const APP_CANVAS_DIV_HEIGHT = window.innerHeight - 44 - 20;
const APP_CANVAS_DIV_WIDTH = 0.49 * APP_CANVAS_DIV_HEIGHT;
const APP_CANVAS_BORDER_WIDTH = 4;

let progressIds = [] as string[];
const dialogInputWidth = 200;

const TileEditor: React.FC<EditorProps> = props => {
  const {
    platform,
    changeAppConfig,
    fetchOrgs,
    saveAppState,
    editorCopyAction,
    editorPasteAction,
    params,
    configureDatasources,
    clearDatasourcesCredentials,
    softRestartConfig,
    changeAppContextData,
    initApptileTilesMode,
    editor,
    orgs,
  } = props;
  const [chatConfig, setChatConfig] = useState<null | {
    agentName: string;
    historyApi: string;
    completionApi: string;
    usePlanner: boolean;
  }>(null);
  const dispatch = useDispatch();
  const {height: windowHeight, width: windowWidth} = useWindowDimensions();
  const pasteInputNode: Ref<any> = useRef();
  const justForCompat = useRef(null);
  const isEditable = useIsEditable();
  const toggleIsEditable = () => dispatch(initApptileIsEditable(!isEditable));
  const hasCurrentAppConfig = useSelector(selectAppConfig) !== undefined;
  const isChatRunning = useSelector((state: EditorRootState) => state.ai.isChatRunning);
  const aiModal = useSelector((state: EditorRootState) => state.ai.aiModal);
  const appId = params?.id;
  useEffect(() => {
    if (hasCurrentAppConfig && appId) {
      dispatch(configureDatasources(appId, true));
    }
  }, [hasCurrentAppConfig, dispatch, configureDatasources, appId]);

  const toggleChat = () => {
    dispatch(toggleChatView());
  };
  const focusedElMeta = useRef({
    id: '',
    pluginName: '',
    componentType: '',
    key: '',
    element: null as Element | null,
    hoveredId: '',
  });
  const [selectedElementUniqueKey, setSelectedElementUniqueKey] = useState<string>('');
  const [modalButtonHovered, setmodalButtonHovered] = useState(false);
  const showChat = editor.showChatView;
  const elements = useRef<{
    leftPanel: null | HTMLDivElement;
    rightPanel: null | Resizable;
    appCanvas: null | InstanceType<typeof Animated.View>;
    hoveredBoundary: null | HTMLDivElement;
    selectedBoundary: null | HTMLDivElement;
    propHoverBoundaries: HTMLDivElement[];
    chatDialog: null | HTMLDialogElement;
    inlineChatText: null | HTMLDivElement;
  }>({
    leftPanel: null,
    rightPanel: null,
    appCanvas: null,
    hoveredBoundary: null,
    selectedBoundary: null,
    propHoverBoundaries: [],
    chatDialog: null,
    inlineChatText: null,
  }).current;

  const isPreview = useIsPreview();
  const navigate = useNavigate();
  const prompterInputRef = useRef();

  useMountEffect(() => {
    const appId = params?.id ?? 1;
    const orgId = params?.orgId;
    const forkId = params?.forkId;
    const branchName = params?.branchName;
    if (appId && orgId && forkId && branchName) {
      changeAppConfig(appId, orgId, forkId, branchName);
    }
    fetchOrgs();
    dispatch(initApptileIsEditable(false));
    // Check if we should automatically open the chat view
    if (appId) {
      // Use a small timeout to ensure the editor is fully loaded
      setTimeout(() => {
        console.log('Auto-opening chat view from URL parameter');
        setChatConfig({
          agentName: 'Architect',
          historyApi: `${window.PLUGIN_SERVER_URL}/plugins/chathistory/v2/${appId}`,
          completionApi: `${window.PLUGIN_SERVER_URL}/home/<USER>/prompt`,
          usePlanner: true,
        });
        dispatch(openChatView());
      }, 1000);
    }
  });

  useEffect(() => {
    initApptileTilesMode(false);
    if (elements.chatDialog) {
      elements.chatDialog.style.display = 'none';
    }
  }, []);

  const showPropertyPanel = () => {
    const selectedElement = focusedElMeta.current.element;
    const reduxState = store.getState();
    let pluginRootEl = selectedElement?.closest('[id^="rootElement-"]');
    if (!pluginRootEl) {
      pluginRootEl = selectedElement?.closest('[id^="boundary-"]');
    }
    const pluginIdAttr = pluginRootEl?.getAttribute('id');
    const pageKey = reduxState.activeNavigation.activePageKey;
    const pluginId =
      pluginIdAttr && pluginIdAttr.startsWith('rootElement')
        ? pluginIdAttr?.substring('rootElement-'.length)
        : pluginIdAttr && pluginIdAttr.startsWith('boundary')
        ? pluginIdAttr?.substring('boundary-'.length)
        : null;
    if (pluginId) {
      store.dispatch({
        type: DispatchActions.SELECT_PLUGIN_WITH_TARGET,
        payload: {
          selector: [pageKey, pluginId],
          target: focusedElMeta.current.id,
        },
      });
    } else {
      alert('Could not find the plugin to edit!');
    }
  };

  const showInlineChat = () => {
    if (elements.chatDialog) {
      elements.chatDialog.style.display = 'block';
      elements.chatDialog.style.inset = 'auto';
      elements.chatDialog.style.backgroundColor = 'transparent';
      elements.chatDialog.showModal();
      const root: HTMLDivElement | null = elements.chatDialog.querySelector('#modal-root');
      const chatArrow: HTMLDivElement | null = elements.chatDialog.querySelector('#modal-arrow');
      const chatBtn = document.querySelector('#selected-btn-chat');
      if (root && chatBtn && chatArrow) {
        const rect = chatBtn.getBoundingClientRect();
        // const selfRect = root.getBoundingClientRect();
        root.style.position = 'fixed';
        root.style.top = rect.bottom + 16 + 'px';
        root.style.left = rect.left - (dialogInputWidth + 20) + 'px';
        chatArrow.style.top = rect.bottom - 10 + 'px';
        chatArrow.style.left = rect.left - 15 + 'px';
      }
    } else {
      alert('Uh oh! Inline chat seems to be broken. But you can still use the sidebar chat');
    }
  };

  const closeInlineChat = (ev?: any) => {
    // Allow closing from X button (no event) or specific target elements
    if (!ev || (ev?.target && ev?.target?.tagName === 'DIALOG') || ev?.target?.getAttribute('id') === 'modal-bgcover') {
      if (elements.chatDialog) {
        if (elements.inlineChatText?.innerHTML) {
          elements.inlineChatText.innerHTML = '';
        }

        elements.chatDialog.style.display = 'none';
        elements.chatDialog.close();
      } else {
        console.error('Failed to close modal');
      }
    }
  };

  const forceCloseInlineChat = () => {
    if (elements.chatDialog) {
      if (elements.inlineChatText?.innerHTML) {
        elements.inlineChatText.innerHTML = '';
      }

      elements.chatDialog.style.display = 'none';
      elements.chatDialog.close();
    } else {
      console.error('Failed to close modal');
    }
  };

  const handleFilePick = useCallback((_ev: React.ChangeEvent<HTMLInputElement>) => {
    // This is now primarily handled by the InlineChatDialog component
    // This function serves as a backup or for additional processing if needed
    console.log('File picker event handled by InlineChatDialog component');
  }, []);

  const handleKeyDown = useCallback((_ev: React.KeyboardEvent<HTMLDivElement>) => {
    // Most key handling is now done in the InlineChatDialog component
    // This can be used for additional processing if needed
  }, []);

  useEffect(() => {
    function narrowToDiv(canvas: any): canvas is HTMLDivElement {
      return !!canvas?.getBoundingClientRect?.call;
    }

    const appCanvas = narrowToDiv(elements.appCanvas) && elements.appCanvas;
    const hoveredElementBoundary = narrowToDiv(elements.hoveredBoundary) && elements.hoveredBoundary;
    const selectedElementBoundary = narrowToDiv(elements.selectedBoundary) && elements.selectedBoundary;

    const showBoundary = (ev: MouseEvent) => {
      if (!isPreview) {
        // const scaleFactor = canvasScale > 0 ? (1/canvasScale) : 1;
        const canvasRect = {top: 0, left: 0};
        if (appCanvas) {
          const rect = appCanvas.getBoundingClientRect();
          canvasRect.left = rect.left;
          canvasRect.top = rect.top;
        }

        if (ev.target instanceof Element) {
          const id = ev.target.getAttribute('id');
          const rect = ev.target.getBoundingClientRect();

          if (id && id !== 'app-canvas' && hoveredElementBoundary) {
            focusedElMeta.current.hoveredId = id;
            hoveredElementBoundary.setAttribute(
              'style',
              `
              position: absolute;
              display: block;
              width: ${rect.width / CANVAS_SCALE}px;
              height: ${rect.height / CANVAS_SCALE}px;
              top: ${(rect.top - canvasRect.top) / CANVAS_SCALE - 4}px;
              left: ${(rect.left - canvasRect.left) / CANVAS_SCALE - 4}px;
              border: dashed 1px ${borderColor};
              border-radius: 1px;
              pointer-events: none;
              box-sizing: border-box;
            `,
            );
          }
        }
      }
    };

    const selectElement = (ev: MouseEvent) => {
      const clickAllowedTargets = ['attempt-fix-button', 'selected-btn-edit', 'selected-btn-chat'];
      let isClickWithinAllowedTargets = false;
      if (ev.target && ev.target.getAttribute) {
        let id = ev.target.getAttribute('id');
        if (!id) {
          id = focusedElMeta.current.hoveredId;
        }
        if (clickAllowedTargets.includes(id)) {
          isClickWithinAllowedTargets = true;
        }

        if (!isClickWithinAllowedTargets) {
          for (let i = 0; i < clickAllowedTargets.length && !isClickWithinAllowedTargets; ++i) {
            const targetId = clickAllowedTargets[i];
            const parent = ev.target.closest('#' + targetId);
            if (!!parent) {
              isClickWithinAllowedTargets = true;
            }
          }
        }
      }

      if (hoveredElementBoundary && !isClickWithinAllowedTargets) {
        hoveredElementBoundary.setAttribute('style', 'display: none;');
      }

      if (!isPreview) {
        if (ev.target && ev.target.getAttribute) {
          if (isClickWithinAllowedTargets) {
            return;
          } else {
            ev.stopPropagation();
            ev.preventDefault();
          }
        }

        if (ev.target instanceof Element) {
          let el = ev.target;
          let id = el.getAttribute('id');
          if (!id && focusedElMeta.current.hoveredId) {
            id = focusedElMeta.current.hoveredId;
            el = document.querySelector('#' + id);
          }
          if (id === 'app-canvas') {
            const nearestPluginEl = ev.target.querySelector('[id^="boundary-"]');
            if (nearestPluginEl) {
              el = nearestPluginEl.children[0];
              id = undefined;
            }
          }

          if (!id) {
            let pluginRoot = el.closest('[id^="rootElement-"]');
            if (!pluginRoot) {
              pluginRoot = el.closest('[id^="boundary-"]');
            }
            if (pluginRoot) {
              const pluginRootId = pluginRoot.getAttribute('id');
              const pluginId = pluginRootId?.split('-')[1];
              const state = store.getState();
              const activePageId = state.activeNavigation.activePageId;
              const pluginName = state.appConfig.current.getIn(['pages', activePageId, 'plugins', pluginId]).subtype;

              if (!pluginRootId || focusedElMeta.current.id === pluginRootId) {
                focusedElMeta.current = {
                  id: '',
                  pluginName: '',
                  componentType: '',
                  key: '',
                  element: null,
                  hoveredId: '',
                };
                if (selectedElementBoundary) {
                  selectedElementBoundary.setAttribute('style', 'display: none;');
                }
                store.dispatch({
                  type: DispatchActions.SELECT_PLUGIN_WITH_TARGET,
                  payload: {
                    selector: null,
                    target: '',
                  },
                });
                return;
              } else {
                focusedElMeta.current = {
                  id: pluginRootId,
                  pluginName,
                  componentType: 'Tile',
                  key: 'root of the plugin',
                  element: pluginRoot,
                  hoveredId: '',
                };
              }
            }
          } else {
            if (focusedElMeta.current.id === id) {
              focusedElMeta.current = {
                id: '',
                pluginName: '',
                componentType: '',
                key: '',
                element: null,
                hoveredId: '',
              };
              store.dispatch({
                type: DispatchActions.SELECT_PLUGIN_WITH_TARGET,
                payload: {
                  selector: null,
                  target: '',
                },
              });
              if (selectedElementBoundary) {
                selectedElementBoundary.setAttribute('style', 'display: none;');
              }
              return;
            } else {
              const parts = id.split('-');
              focusedElMeta.current = {
                id,
                pluginName: parts[0],
                componentType: parts[1],
                key: parts.slice(2).join('-'),
                element: ev.target,
                hoveredId: '',
              };
            }
          }

          const rect = el.getBoundingClientRect();
          if (id) {
            const canvasRect = {top: 0, left: 0, width: 0, height: 0};
            if (appCanvas) {
              const appCanvasRect = appCanvas.getBoundingClientRect();
              canvasRect.left = appCanvasRect.left;
              canvasRect.top = appCanvasRect.top;
              canvasRect.width = appCanvasRect.width;
              canvasRect.height = appCanvasRect.height;
            }

            if (selectedElementBoundary) {
              const padding = 8;

              // Calculate boundary in unscaled coordinates, relative to the canvas
              const boundary = {
                left: rect.left - canvasRect.left - padding,
                top: rect.top - canvasRect.top - padding,
                width: rect.width + 2 * padding,
                height: rect.height + 2 * padding,
              };

              // Clip the boundary to the canvas
              const clipped = {
                left: Math.max(boundary.left, 0),
                top: Math.max(boundary.top, 0),
                right: Math.min(boundary.left + boundary.width, canvasRect.width),
                bottom: Math.min(boundary.top + boundary.height, canvasRect.height),
              };

              const finalWidth = clipped.right - clipped.left;
              const finalHeight = clipped.bottom - clipped.top;

              selectedElementBoundary.setAttribute(
                'style',
                `
                position: absolute;
                display: block;
                width: ${finalWidth / CANVAS_SCALE}px;
                height: ${finalHeight / CANVAS_SCALE}px;
                top: ${clipped.top / CANVAS_SCALE - 4}px;
                left: ${clipped.left / CANVAS_SCALE - 4}px;
                border: solid 2px ${borderColor};
                border-radius: 1px;
                box-sizing: border-box;
                pointer-events: none;
              `,
              );
            }

            showPropertyPanel();
          } else {
            focusedElMeta.current = {
              id: '',
              element: null,
              pluginName: '',
              componentType: '',
              key: '',
              hoveredId: '',
            };
          }
        }
      }

      setSelectedElementUniqueKey(focusedElMeta.current.key || '');
    };

    const hideElementBoundary = (ev: MouseEvent) => {
      if (hoveredElementBoundary) {
        hoveredElementBoundary.setAttribute('style', 'display: none;');
      }
    };

    if (appCanvas) {
      appCanvas.addEventListener('mouseover', showBoundary);
      appCanvas.addEventListener('click', selectElement, true);
      appCanvas.addEventListener('mouseleave', hideElementBoundary);
    }

    if (hoveredElementBoundary && isPreview) {
      hoveredElementBoundary.setAttribute('style', 'display: none;');
    }

    if (selectedElementBoundary && isPreview) {
      selectedElementBoundary.setAttribute('style', 'display: none;');
    }

    return () => {
      if (appCanvas) {
        appCanvas.removeEventListener('mouseover', showBoundary);
        appCanvas.removeEventListener('click', selectElement, true);
        appCanvas.removeEventListener('mouseleave', hideElementBoundary);
      }
    };
  }, [elements, focusedElMeta, isPreview]);

  const addContextToChat = (chatmessage: string) => {
    const reduxState = store.getState();
    const isChatRunning = reduxState?.ai?.isChatRunning ?? false;
    if (isChatRunning) {
      alert('Please wait for the current run to finish!');
    } else {
      // store.dispatch(openChatView());
      if (focusedElMeta.current.pluginName && prompterInputRef.current) {
        // Create a unique tag ID based on plugin name and element ID
        const currentScreen = store.getState().activeNavigation.activePageId;
        prompterInputRef.current.sendContextfulChat(`
Single prompt context<br>
------<br>
+ The element being referred to is: ${focusedElMeta.current.id}<br>
+ The plugin inside which this element is found: ${focusedElMeta.current.pluginName}<br>
+ The plugin is dropped in the screen ${currentScreen}<br>
<br>
Prompt<br>
------<br>
${chatmessage}<br>`);
      }
    }
  };

  const handleSendPrompt = (message: string) => {
    addContextToChat(message);
    // Animate and close the dialog (same logic as Enter key handling)
    const root = elements.chatDialog?.querySelector('#modal-root');
    const arrow = elements.chatDialog?.querySelector('#modal-arrow');
    if (root && arrow && elements.chatDialog) {
      const rootRect = root.getBoundingClientRect();
      const animationFrame = [
        {
          transform: `translateX(-${rootRect.left}px) translateY(${
            window.innerHeight - rootRect.top - rootRect.height
          }px)`,
          opacity: 0.5,
        },
      ];

      const animationOpts = {
        duration: 200,
        easing: 'ease-in',
      };

      arrow.animate(animationFrame, animationOpts);
      root.animate(animationFrame, animationOpts);
      setTimeout(() => {
        forceCloseInlineChat();
      }, 200);
    } else {
      forceCloseInlineChat();
    }
  };

  const [pluginAddLoading, setSluginAddLoading] = useState(false);

  const handleAddTileAbove = useCallback(() => {
    setSluginAddLoading(true);
    const reduxState = store.getState();
    const currentPageId = reduxState.activeNavigation.activePageId;

    if (!currentPageId) {
      alert('No active page found');
      return;
    }

    // Get the first plugin on the current page to use as reference
    const appConfig = reduxState.appConfig.current;
    const pageConfig = appConfig.getPage(currentPageId);
    const firstPluginId = pageConfig?.plugins?.keySeq()?.first();

    // Create a simple plugin name with timestamp to ensure uniqueness
    const timestamp = Date.now();
    const pluginName = `NewPlugin${timestamp}`;
    const registryName = pluginName.toLowerCase();

    // Create the plugin via API call (similar to handleAdd in PluginListingControl)
    fetch(`${window.PLUGIN_SERVER_URL}/plugins/${appId}/create`, {
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      body: JSON.stringify({
        labelPrefix: registryName,
        listingName: pluginName,
        displayDescription: 'Basic plugin created from template',
        listingIcon: 'badge',
        pluginRegistryName: registryName,
      }),
    })
      .then(() => {
        return fetch(`${window.PLUGIN_SERVER_URL}/plugins/${appId}/compileall`, {
          method: 'POST',
        });
      })
      .then(() => {
        // Reload external plugins first
        return reloadExternalPlugins({uuid: appId});
      })
      .then(() => {
        // Add the plugin to the top of the current page
        if (firstPluginId) {
          // If there are existing plugins, add before the first one
          store.dispatch({
            type: 'ADD_PLUGIN',
            payload: {
              afterRefWidget: false,
              configType: 'widget',
              container: '',
              pageId: currentPageId,
              pluginType: registryName,
              refWidget: firstPluginId,
            },
          });
        } else {
          // If no plugins exist, just add it normally
          store.dispatch({
            type: 'ADD_PLUGIN',
            payload: {
              afterRefWidget: false,
              configType: 'widget',
              container: '',
              pageId: currentPageId,
              pluginType: registryName,
              refWidget: '',
            },
          });
        }
        setSluginAddLoading(false);
      })
      .catch(error => {
        console.error('Error creating and adding plugin:', error);
        alert('Failed to create and add plugin. Please try again.');
        setSluginAddLoading(false);
      });
  }, [appId, softRestartConfig]);
  useEffect(() => {
    if (appId) {
      changeAppContextData(appId);
    }
  }, [appId, changeAppContextData]);

  useEffect(() => {
    dispatch(fetchAppBranchesWithScheduledOta(appId as string, params?.forkId));
  }, []);

  const apptileState = useSelector(state => state.apptile);

  const scale = useSharedValue(CANVAS_SCALE);
  const translateY = useSharedValue(0);

  useEffect(() => {
    scale.value = withTiming(isPreview ? 1 : CANVAS_SCALE, {
      duration: 200,
      easing: Easing.linear,
    });
    translateY.value = withTiming(isPreview ? -22 : 0, {
      duration: 200,
      easing: Easing.linear,
    });
  }, [isPreview]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{scale: scale.value}, {translateY: translateY.value}],
    };
  });

  const getHotkeyHandlers = useCallbackRef(() => {
    return {
      EDITOR_PASTE: () => {
        logger.info('Pasting');
        pasteInputNode?.current.focus();
      },
      EDITOR_COPY: editorCopyAction,
      PUBLISH: () => {
        let message = window && window.prompt('Please enter publish message', 'Updated template');
        if (message) saveAppState(true, true, message || 'Updated template');
      },
      SAVE: () => {
        console.log("Page has been saved using 's' key");
        saveAppState(false, true);
      },
      SOFT_REFRESH: () => {
        console.log("Soft refresh has been triggered using 'r' key");
        softRestartConfig();
      },
      TOGGLE_EDIT: () => {
        console.log("Toggle edit mode has been triggered using 'e' key");
        toggleIsEditable();
      },
    };
  });

  let chatView = null;
  if (showChat && chatConfig) {
    chatView = (
      <AIClient
        ref={prompterInputRef}
        appId={appId}
        themeColors={themeColors}
        historyApi={chatConfig.historyApi}
        completionApi={chatConfig.completionApi}
        editorView={justForCompat}
        chatWidthInPercentage="100%"
        agentName={chatConfig.agentName}
        onArchitectClick={() => {
          setChatConfig({
            agentName: 'Architect',
            historyApi: `${window.PLUGIN_SERVER_URL}/plugins/chathistory/v2/${appId}`,
            completionApi: `${window.PLUGIN_SERVER_URL}/home/<USER>/prompt`,
            usePlanner: true,
          });
        }}
        onCompletionRunDone={err => {
          if (err) {
            if (err.type === 'network error') {
              dispatch({
                type: 'SET_AI_MODAL',
                payload: {
                  visibility: true,
                  prompt: 'Code generation stopped due to a momentary internet connectivity issue! You can ask the agent to continue if connectivity has been restored.',
                },
              });
              // alert(
              //   'Code generation stopped due to a momentary internet connectivity issue! You can ask the agent to continue if connectivity has been restored.',
              // );
            } else {
              console.error('[AGENT] Chat completion error: ', err);
            }
            console.log('[AGENT] Chat completion error: ', err);
          } else {
            console.log('[AGENT] Planner agent saga finished!');
          }
        }}
        usePlanner={chatConfig.usePlanner}
      />
    );
  }

  const chatViewX = useSharedValue(isPreview ? -450 : 0);

  useEffect(() => {
    chatViewX.value = withTiming(isPreview ? -450 : 0, {
      duration: 300,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview]);

  const chatViewStyle = useAnimatedStyle(() => {
    return {
      transform: [{translateX: chatViewX.value}],
    };
  });

  const CloneAndOpenButton = useCallback(() => {
    return <button onClick={() => cloneOpenApp(appId)}>Clone and open</button>;
  }, [appId]);

  if (!appId) {
    return <div>Loading...</div>;
  }

  const topbarHeight = 44 + 16 + 16;

  const handleModalDismiss = useCallback(() => {
    const prompt = aiModal.prompt;
    dispatch({
      type: 'SET_AI_MODAL',
      payload: {
        visibility: false,
        prompt: '',
      },
    });

    if (prompterInputRef.current && !isChatRunning) {
      prompterInputRef.current.sendContextfulChat(prompt);
    } else {
      console.error('Whoops! All fallbacks have failed.');
    }
  }, [prompterInputRef, isChatRunning, aiModal]);
  
  useEffect(() => {
    if (aiModal.visible && prompterInputRef.current) {
      prompterInputRef.current.resetChat(); 
    }
  }, [aiModal]);

  return (
    <EditorContext.Provider value={{layout: 'editorV1'}}>
      {/* <style>{`
        * {
          box-sizing: border-box;
        }
      `}</style> */}
      <HotKeys
        component={'div'}
        keyMap={HOTKEY_MAPS.EDITOR_GLOBAL}
        handlers={getHotkeyHandlers()}
        className="hot-keys"
        style={{
          backgroundColor: '#34363d',
          backgroundImage: `radial-gradient(circle, #4a4a4a 1px, transparent 1px)`,
          backgroundSize: '20px 20px',
          height: windowHeight,
          display: 'flex',
          flexDirection: 'row',
          outline: 'none',
        }}>
        <LeftSidebar mainBar="APP_EDITOR" />
        <div
          className="thin-sidebar-content"
          style={{
            display: 'flex',
            flexDirection: 'row',
            width: 'calc(100vw - 60px)',
          }}>
          <ThemeContainer>
            <div
              id="preview-module-root"
              style={{
                display: 'flex',
                width: 476,
                marginLeft: theme.THIN_GAP,
                paddingTop: theme.THIN_GAP,
                paddingBottom: theme.THIN_GAP,
                transition: 'transform 0.2s',
                position: isPreview ? 'static' : 'absolute',
                transform: isPreview ? 'none' : 'translateX(-500px)',
              }}>
              <PreviewModule appId={params.id} />
            </div>
            <div
              id="chat-panel-root"
              style={{
                display: 'flex',
                width: 476,
                marginLeft: theme.THIN_GAP,
                paddingTop: theme.THIN_GAP,
                paddingBottom: theme.THIN_GAP,
                flexDirection: 'column',
                transition: 'transform 0.2s',
                position: isPreview ? 'absolute' : 'static',
                transform: isPreview ? 'translateX(-500px)' : 'none',
              }}>
              <AppNameEditor />
              {chatView}
            </div>
            {/* <TopBar /> */}
            <div
              style={{
                // height: '100%',
                // position: 'absolute',
                // top: '50%',
                // left: '50%',
                // transform: 'translate(-50%, -50%)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: window.innerWidth - 476 - 366 - 60 - 6,
                paddingTop: theme.THIN_GAP,
              }}>
              <div
                style={{
                  transition: 'transform 0.2s',
                  position: 'relative',
                  transform: isPreview ? 'translateY(-50px)' : 'none',
                }}>
                {<ScreenSelector />}
              </div>
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Animated.View
                  style={[styles.appContainer, animatedStyle]}
                  id="app-canvas"
                  ref={el => (elements.appCanvas = el)}>
                  <ApptileCanvasScaleContext.Provider value={1}>
                    <View style={styles.appCanvas}>
                      <ApptileApp />
                    </View>
                  </ApptileCanvasScaleContext.Provider>
                  <div
                    ref={el => (elements.selectedBoundary = el)}
                    id="selected-element-boundary"
                    style={{display: 'none'}}>
                    {borderDots}
                    <div
                      id="btn-container"
                      style={{
                        position: 'absolute',
                        top: -16,
                        // width: 2 * 28 + 19,
                        right: 0,
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-evenly',
                      }}>
                      {/* <button id="selected-btn-edit" style={styles.selectedElButtons} onClick={showPropertyPanel}>
                        {edit}
                      </button> */}
                      <button id="selected-btn-chat" style={styles.selectedElButtons} onClick={showInlineChat}>
                        {edit}
                      </button>
                    </div>
                  </div>
                  <div id="hover-target-1" style={{display: 'none'}} />
                  <div id="hover-target-2" style={{display: 'none'}} />
                  <div id="hover-target-3" style={{display: 'none'}} />
                  <div id="hover-target-4" style={{display: 'none'}} />
                  <div id="hover-target-5" style={{display: 'none'}} />
                  <div
                    id="hovered-element-boundary"
                    ref={el => (elements.hoveredBoundary = el)}
                    style={{display: 'none'}}
                  />
                </Animated.View>
              </View>
            </div>
            <div
              className={'right-sidebar-root'}
              style={{
                width: 366,
                paddingTop: theme.THIN_GAP,
                paddingBottom: theme.THIN_GAP,
                paddingRight: theme.THIN_GAP,
                display: 'flex',
                flexDirection: 'column',
              }}>
              <TopRightControls />
              <RightSidebar
                selectedElementUniqueKey={selectedElementUniqueKey}
                onChat={() => {
                  const state = store.getState();
                  const pluginConfig = selectSelectedPluginConfig(state);
                  const pluginName = pluginConfig.get('subtype');
                  setChatConfig({
                    agentName: `Developer:${pluginName}`,
                    historyApi: `${window.PLUGIN_SERVER_URL}/plugins/chathistory/v2/${appId}`,
                    completionApi: `${window.PLUGIN_SERVER_URL}/plugins/prompt/${appId}/${pluginName}`,
                    usePlanner: false,
                  });

                  store.dispatch(openChatView());
                }}
                onEditCode={(pluginName: string) =>
                  navigate(
                    `/dashboard/${apptileState?.orgId}/app/${apptileState?.appId}/f/${apptileState?.forkId}/b/${apptileState?.appBranch}/dashboard/codeEditor/${pluginName}`,
                  )
                }
              />
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                height: windowHeight - topbarHeight,
              }}>
              <div
                style={{
                  flex: 1,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}></div>
            </div>
          </ThemeContainer>
        </div>
      </HotKeys>
      <BetaBuildTag />
      <WebSDKBetaBuildTag />
      <View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
        }}>
        {/* <SupabaseConnectButton /> */}
        {/* <CloneAndOpenButton /> */}
      </View>
      {/* <dialog ref={el => (elements.chatDialog = el)} onClose={closeInlineChat} style={styles.modalDialog}>
        <div id="modal-bgcover" style={styles.modalBgCover} onClick={closeInlineChat}>
          <div id="modal-arrow" style={styles.modalArrow}></div>
          <div id="modal-root" style={styles.modalRoot}>
            <div
              ref={el => (elements.inlineChatText = el)}
              style={styles.modalText}
              contentEditable={true}
              onKeyDown={handleKeyDown}></div>
            <div style={styles.modalImageBtn}>
              {plus}
              <input
                type="file"
                style={styles.hiddenFileInput}
                onChange={handleFilePick}
                accept="image/png,image/jpeg"
              />
            </div>
          </div>
        </div>
      </dialog> */}
      <InlineChatDialog
        onClose={forceCloseInlineChat}
        handleFilePick={handleFilePick}
        handleKeyDown={handleKeyDown}
        innerRef={el => (elements.chatDialog = el)}
        onSendPrompt={handleSendPrompt}
      />
    <RecheckModal isVisible={aiModal.visible} onModalClose={handleModalDismiss}/>
    </EditorContext.Provider>
  );
};

const styles = StyleSheet.create({
  editorLayout: {
    display: 'flex',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    // alignItems: 'stretch',
    // alignContent: 'stretch',
    overflow: 'hidden',
  },
  launchButtonWrapper: {
    width: 117,
    borderRadius: 8,
    backgroundColor: '#0062FF',
    borderColor: '#0062FF',
    position: 'absolute',
    top: 10,
    right: 10,
  },
  deviceBezel: {
    backgroundColor: 'transparent',
    shadowColor: 'black',
    shadowOffset: {width: 10, height: 10},
    shadowRadius: 50,
    shadowOpacity: 0.4,
    overflow: 'visible',
  },
  leftPanel: {
    flex: 1,
    // maxWidth: 280,
    minWidth: 280,
    flexDirection: 'column',
    shadowColor: '#000000',
    alignItems: 'stretch',
    alignContent: 'stretch',
    flexGrow: 1,
    shadowOffset: {
      width: 5,
      height: 0,
    },
    shadowRadius: 10,
    shadowOpacity: 0.3,
    height: '100%',
  },
  leftPanelTop: {
    height: 'auto',
    flex: 1,
    flexBasis: 'auto',
    flexGrow: 0,
  },
  leftPanelAdj: {
    height: 'auto',
    flex: 1,
    flexBasis: 'auto',
    flexGrow: 0,
    position: 'absolute',
    top: 20,
    right: -48,
    width: 48,
    maxWidth: 48,
  },
  rowContainer: {
    height: 'auto',
    flex: 1,
    flexDirection: 'row',
    flexBasis: 'auto',
    flexGrow: 0,
    alignItems: 'stretch',
    marginLeft: 0,
  },
  leftScrollContainer: {
    flex: 1,
    padding: 2,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    fontSize: 12,
    flexBasis: 'auto',
  },
  appContainer: {
    position: 'relative',
    width: APP_CANVAS_DIV_WIDTH + 2 * APP_CANVAS_BORDER_WIDTH,
    height: APP_CANVAS_DIV_HEIGHT + 2 * APP_CANVAS_BORDER_WIDTH,
    borderRadius: 50,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    alignSelf: 'center',
    overflow: 'visible',
    backgroundColor: 'rgba(0,0,0,0)',
    borderWidth: APP_CANVAS_BORDER_WIDTH,
    borderColor: 'black',
    transform: [{scale: CANVAS_SCALE}],
  },
  appCanvas: {
    top: 0,
    left: 0,
    width: APP_CANVAS_DIV_WIDTH,
    height: APP_CANVAS_DIV_HEIGHT,
    position: 'absolute',
    borderRadius: 45,
    overflow: 'hidden',
  },
  rightPanel: {
    flex: 1,
    // maxWidth: 375,
    minWidth: 375,
    height: '100%',
    flexDirection: 'column',
    shadowColor: '#000000',
    shadowOffset: {
      width: -5,
      height: 0,
    },
    shadowRadius: 10,
    shadowOpacity: 0.3,
  },
  ml4: {marginLeft: 4},
  navButton: {
    flex: 1,
    backgroundColor: 'rgb(33, 150, 243)',
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginBottom: 2,
    borderRadius: 5,
  },
  QRPopover: {
    backgroundColor: theme.TILE_BACKGROUND,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 15,
    borderWidth: 1,
  },
  selectedElButtons: {
    outline: 'none',
    backgroundColor: 'white',
    border: `solid 2px ${borderColor}`,
    borderRadius: 14,
    height: 28,
    width: 28,
    padding: 0,
    cursor: 'pointer',
    pointerEvents: 'all',
  },
  modalDialog: {
    overflow: 'hidden',
  },
  modalBgCover: {
    width: '100vw',
    height: '100vh',
    position: 'relative',
  },
  modalRoot: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: borderColor,
    borderStyle: 'solid',
    backgroundColor: '#292B32',
    padding: 12,
    display: 'flex',
    flexDirection: 'row',
  },
  modalArrow: {
    backgroundColor: '#292B32',
    borderColor: borderColor,
    borderStyle: 'solid',
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 3,
    borderWidth: 1,
    // transform: [{rotateZ: "45Deg"}]
    transform: 'rotateZ(45deg)',
  },
  modalText: {
    backgroundColor: '#596470',
    color: 'white',
    lineHeight: '30px',
    outline: 'none',
    borderRadius: 4,
    width: dialogInputWidth,
    paddingLeft: 4,
    paddingRight: 4,
  },
  modalImageBtn: {
    width: 30,
    height: 30,
    borderRadius: 4,
    marginLeft: 12,
    backgroundColor: '#35404D',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  hiddenFileInput: {
    width: 30,
    height: 30,
    position: 'absolute' as const,
    opacity: 0,
    cursor: 'pointer',
  },
  aiModalText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '500',
    fontFamily: 'General Sans',
    lineHeight: 20,
  },
});
function withParams(Component: any) {
  return (props: JSX.IntrinsicAttributes) => <Component {...props} params={useParams()} />;
}

export default withParams(TileEditor);
