import { Modal , View, Text, Pressable, StyleSheet} from "react-native";
import GradientLoader from "@/root/web/components/GradientLoader";
import { useEffect, useRef, useState } from "react";
import theme, { colors } from "../../styles-prompt-to-app/theme";



const RecheckModal = ({isVisible, onModalClose}:any) =>{
    const [modalButtonHovered, setmodalButtonHovered] = useState(false);
    const autoTriggerAt = useRef(0);
    const delayRef = useRef(0);
    const countdownRef = useRef(null);

    useEffect(() => {
    
        let timeout = null;
    
        if (isVisible) {
            let delay;
    
            if (delayRef.current) {
                delay = delayRef.current;
                delayRef.current = Math.min(2 * delay, 2 * 60 * 60 * 1000); // cap at 2 hours
            } else {
                delay = 2000;
                delayRef.current = delay;
            }
    
            autoTriggerAt.current = Date.now() + delay;
    
           const interval = setInterval(() => {
                const remaining = Math.floor((autoTriggerAt.current - Date.now()) / 1000);
    
                if (countdownRef.current) {
                    countdownRef.current.innerText = remaining;
                }

    
                if (remaining <= 0) {
                    clearInterval(interval);
                }
            }, 1000);
    
            timeout = setTimeout(() => {
                onModalClose();
            }, delay);
        }
    
        return () => {
            clearTimeout(timeout);
        };
    }, [isVisible, autoTriggerAt]);
    
    const onManualModalClose =()=>{
        delayRef.current=1000;
        onModalClose();
    }

    return (
        <Modal
        transparent
        visible={isVisible}
        style={{width: '100vw', height: '100vh'}}
        onRequestClose={() => {
        }}>
        <View
          style={{
            width: '100%',
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: '#444444cc',
          }}>
          <View
            style={{
              width: '100%',
              height: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#44444454',
            }}>
            <View
              style={{
                width: 600,
                height: 250,
                borderWidth: 1,
                borderColor: '#3D3D3D',
                borderRadius: theme.CORNER_RADIUS_HALF,
                flexDirection: 'column',
                justifyContent: 'space-between',
                paddingHorizontal:32,
                paddingVertical: 48,
                alignItems: 'center',
                backgroundColor: '#121212'
              }}>
                <div style={{flexDirection:'row', display:'flex'}}>
                <Text style={styles.aiModalText}>Please Wait! The agent is rebooting in </Text>
              <div style={{color: '#1060E0',  justifyContent:'center', fontWeight:500, alignItems:'center', minWidth:16, marginRight:1, marginLeft:1}} ref={countdownRef}>..</div>
                 <Text style={styles.aiModalText}>seconds...</Text>
                </div>
              <View style={{flexDirection: 'row'}}>
                <GradientLoader size={28} thickness={2} containerStyle={{margin:10}} backgroundColor='rgba(0, 0, 0, 0.9)' />
              </View>
              <Pressable
                style={{
                  flexDirection: 'row',
                  paddingVertical: 12,
                  paddingHorizontal: 10,
                  backgroundColor: modalButtonHovered ? '#004999B2' : '#1060E0B2',
                  borderRadius: theme.CORNER_RADIUS_HALF / 2,
                }}
                onHoverIn={() => setmodalButtonHovered(true)}
                onHoverOut={() => setmodalButtonHovered(false)}
                onPress={onManualModalClose}
                >
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: '500',
                    color: theme.FOREGROUND,
                    fontFamily: 'General Sans',
                    paddingLeft: theme.THIN_GAP,
                  }}>
                Re-Check Now
                </Text>
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>
    )
}

const styles= StyleSheet.create({
    aiModalText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: '500',
        fontFamily: 'General Sans',
        lineHeight: 20,
      },
})
export default RecheckModal