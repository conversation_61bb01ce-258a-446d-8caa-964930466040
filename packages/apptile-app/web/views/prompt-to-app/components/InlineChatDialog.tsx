import React, {useState, useRef, CSSProperties} from 'react';
import {CirclePlus, plus, SendButton, SendButtonSmall} from '@/root/web/views/prompt-to-app/editor/components/svgelems';
import theme from '../styles-prompt-to-app/theme';
import { Icon } from 'apptile-core';

type InlineChatDialogProps = {
  onClose: () => void;
  handleFilePick?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleKeyDown?: (e: React.KeyboardEvent<HTMLDivElement>) => void;
  innerRef: React.RefObject<HTMLDialogElement> | ((el: HTMLDialogElement) => void);
  onSendPrompt?: (message: string) => void;
};

const InlineChatDialog: React.FC<InlineChatDialogProps> = ({
  onClose,
  handleFilePick,
  handleKeyDown,
  innerRef,
  onSendPrompt,
}) => {
  const [_message, setMessage] = useState<string>('');
  const [hoveredItem, setHoveredItem] = useState('');
  const [hovered, setHovered] = useState(false);
  const contentEditableRef = useRef<HTMLDivElement>(null);

  const handleInput = (e: React.ChangeEvent<HTMLDivElement>) => {
    setMessage(e.target.textContent || '');
  };

  const handleFilePickInternal = (ev: React.ChangeEvent<HTMLInputElement>) => {
    const file = ev.target.files?.[0];
    if (file) {
      const fileReader = new FileReader();
      fileReader.onload = e => {
        const dataUrl = e.target?.result?.toString();
        if (dataUrl && contentEditableRef.current) {
          const existingContent = contentEditableRef.current.innerHTML;
          contentEditableRef.current.innerHTML = existingContent + `<img src="${dataUrl}">`;
          // Trigger input event to update any state that depends on content changes
          const inputEvent = new Event('input', {bubbles: true});
          contentEditableRef.current.dispatchEvent(inputEvent);
        } else {
          console.error("Could not access contentEditable div or didn't get data from file");
        }
        ev.target.value = '';
      };

      fileReader.onerror = err => {
        console.error('Failed to read file', err);
        ev.target.value = '';
      };

      fileReader.readAsDataURL(file);
    } else {
      console.log('Ignoring event: ', ev);
    }

    // Also call the external handler if provided
    if (handleFilePick) {
      handleFilePick(ev);
    }
  };

  const handleKeyDownInternal = (ev: React.KeyboardEvent<HTMLDivElement>) => {
    if (ev.shiftKey && ev.key === 'Enter' && contentEditableRef.current) {
      // Handle Shift+Enter for new line
      ev.preventDefault();
      const newLine = document.createElement('div');
      contentEditableRef.current.appendChild(newLine);
      const range = document.createRange();
      range.selectNodeContents(newLine);
      range.collapse(false);

      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } else if (ev.key === 'Enter' && contentEditableRef.current && onSendPrompt) {
      // Handle Enter for sending
      ev.preventDefault();
      const messageContent = contentEditableRef.current.innerHTML;
      if (messageContent.trim()) {
        onSendPrompt(messageContent);
        // Clear the input after sending
        contentEditableRef.current.innerHTML = '';
      }
    }

    // Also call the external handler if provided
    if (handleKeyDown) {
      handleKeyDown(ev);
    }
  };

  const dialogStyle: CSSProperties = {
    backgroundColor: 'transparent',
    border: 'none',
    padding: 0,
    margin: 0,
    maxWidth: 'none',
    maxHeight: 'none',
    overflow: 'visible',
  };

  const containerStyle: CSSProperties = {
    width: '400px',
    backgroundColor: theme.BACKGROUND,
    borderRadius: '20px',
    border: '2px solid #4285f4',
    padding: '16px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    zIndex: 1000,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    position: 'relative',
    transform: 'translateY(-17px)',
  };

  const triangleStyle: CSSProperties = {
    position: 'absolute',
    top: '-10px',
    left: '25%',
    transform: 'translateX(-50%)',
    width: '0',
    height: '0',
    borderLeft: '10px solid transparent',
    borderRight: '10px solid transparent',
    borderBottom: '10px solid #4285f4',
    zIndex: 1001,
  };

  const triangleInnerStyle: CSSProperties = {
    position: 'absolute',
    top: '-8px',
    left: '25%',
    transform: 'translateX(-50%)',
    width: '0',
    height: '0',
    borderLeft: '8px solid transparent',
    borderRight: '8px solid transparent',
    borderBottom: '8px solid #2E3134',
    zIndex: 1002,
  };

  const closeButtonStyle: CSSProperties = {
    position: 'absolute',
    right: 8,
    background: 'none',
    border: 'none',
    color: '#9aa0a6',
    cursor: 'pointer',
    fontSize: '16px',
    padding: '0',
    width: '20px',
    height: '20px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '2px',
  };

  const inputAreaStyle: CSSProperties = {
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    marginBottom: '16px',
    position: 'relative',
    paddingRight: 20,
    overflow: 'hidden',
  };

  const inputStyle: CSSProperties = {
    width: '100%',
    backgroundColor: theme.BACKGROUND,
    border: 'none',
    color: '#ffffff',
    fontSize: '14px',
    outline: 'none',
    minHeight: '20px',
    maxHeight: '120px',
    resize: 'none',
    fontFamily: 'inherit',
    overflow: 'auto',
    overflowX: 'hidden',
    lineHeight: '1.4',
    wordWrap: 'break-word',
    whiteSpace: 'pre-wrap',
  };

  const bottomBarStyle: CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  };

  const addButtonStyle: CSSProperties = {
    width: '30px',
    height: '30px',
    // borderRadius: '4px',
    // backgroundColor: '#35404D',
    // border: 'none',
    color: '#9aa0a6',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '18px',
    position: 'relative',
    borderRadius: '16.876px',
    // border: '0.844px solid #C4C4C4',
  };

  const sendButtonStyle: CSSProperties = {
    // width: '30px',
    // height: '30px',
    borderRadius: '6px',
    backgroundColor: 'transparent',
    border: 'none',
    color: '#ffffff',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '16px',
  };

  const fileInputStyle: CSSProperties = {
    width: 30,
    height: 30,
    position: 'absolute',
    opacity: 0,
    cursor: 'pointer',
  };

  // Add global styles for images in the contentEditable
  const globalImageStyles = `
    #modal-root [contenteditable] img {
      max-width: 63px;
      border-radius: 8px;
      margin: 4px 0;
      display: inline-block;
      vertical-align: middle;
    }
    
    #modal-root [contenteditable]:empty::before {
      content: 'Ask Tile...';
      color: #ffffff80;
      opacity: 0.5;
      font-size: 14px;
      pointer-events: none;
    }
    
    #modal-root [contenteditable]::-webkit-scrollbar {
      width: 4px;
    }
    
    #modal-root [contenteditable]::-webkit-scrollbar-track {
      background: transparent;
    }
    
    #modal-root [contenteditable]::-webkit-scrollbar-thumb {
      background: #5f6368;
      border-radius: 2px;
    }
  `;

  return (
    <dialog style={dialogStyle} ref={innerRef}>
      {/* Inject CSS styles for images and scrollbar */}
      <style dangerouslySetInnerHTML={{__html: globalImageStyles}} />
      {/* Triangle pointer */}
      <div id="modal-arrow" style={triangleStyle} />
      <div style={triangleInnerStyle} />
      <div style={containerStyle} id="modal-root" onClick={e => e.stopPropagation()}>
        <div style={inputAreaStyle}>
          <div
            ref={contentEditableRef}
            contentEditable
            style={inputStyle}
            onInput={handleInput}
            onKeyDown={handleKeyDownInternal}
            suppressContentEditableWarning={true}
          />
          <button style={closeButtonStyle} onClick={onClose}>
            <Icon 
              iconType='MaterialIcons'
              name='close'
              style={{color: theme.FOREGROUND, opacity: 0.7}}
              size={20}
            />
          </button>
        </div>
        <div style={bottomBarStyle} onClick={e => e.stopPropagation()}>
          <div 
            style={addButtonStyle} 
            onMouseEnter={() => {
              setHovered(true)
            }} 
            onMouseLeave={() => {
              setHovered(false)
            }}
          >
            <CirclePlus isHovered={hovered}></CirclePlus>
            <input type="file" style={fileInputStyle} onChange={handleFilePickInternal} accept="image/png,image/jpeg" />
          </div>

          <button
            style={sendButtonStyle}
            onMouseEnter={() => {
              setHoveredItem('prompterInputSendButton');
            }}
            onMouseLeave={() => {
              setHoveredItem('');
            }}
            onClick={() => {
              if (contentEditableRef.current && onSendPrompt) {
                const messageContent = contentEditableRef.current.innerHTML;
                onSendPrompt(messageContent);
                // Clear the input after sending
                contentEditableRef.current.innerHTML = '';
              }
            }}>
            <SendButtonSmall isHovered={hoveredItem === 'prompterInputSendButton'} />
          </button>
        </div>
      </div>
    </dialog>
  );
};

export default InlineChatDialog;
