import React, {useState} from 'react';
import {useSelector, useDispatch} from 'react-redux';
import VoucherApi from '@/root/web/api/VoucherApi';
import { useParams } from 'react-router-dom';
import { makeToast } from '@/root/web/actions/toastActions';

interface IRedeemVoucherModal {
  onClose: () => void;
  onRedeem: () => void;
}

const RedeemVoucherModal: React.FC<IRedeemVoucherModal> = ({onClose, onRedeem}) => {
  const params = useParams();
  const dispatch = useDispatch();
  const [voucherCode, setVoucherCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const userEmail = useSelector((state: any) => state.user?.user?.email);
  const organizationId = params.orgId;

  const handleRedeem = async () => {
    if (!voucherCode.trim()) {
      dispatch(makeToast({content: 'Please enter a voucher code', appearances: 'warning'}));
      return;
    }

    if (!userEmail || !organizationId) {
      dispatch(makeToast({content: 'User information not found. Please try logging in again.', appearances: 'warning'}));
      return;
    }

    setIsLoading(true);

    try {
      const response = await VoucherApi.redeemVoucher(voucherCode, userEmail, organizationId);
      dispatch(makeToast({content: 'Voucher redeemed', appearances: 'success'}));
      onRedeem();
      onClose();
    } catch (err: any) {
      dispatch(makeToast({content: err.response?.data?.message || 'Failed to redeem voucher. Please try again.', appearances: 'error'}));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div style={styles.modalContainer}>
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'flex-end',
            borderRadius: '50px',
            height: 'fit-content',
          }}>
          <div onClick={onClose} style={styles.closeButton}>
            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
              <path d="M2 2L13 13" stroke="white" stroke-width="2" stroke-linecap="round" />
              <path d="M13 2L2 13" stroke="white" stroke-width="2" stroke-linecap="round" />
            </svg>
          </div>
        </div>

        <p style={styles.title}>{'Redeem Voucher'}</p>
        
        <div style={styles.inputContainer}>
          <input
            type="text"
            value={voucherCode}
            onChange={(e) => setVoucherCode(e.target.value)}
            placeholder="Enter voucher code"
            style={styles.input}
          />
          <button 
            onClick={handleRedeem} 
            disabled={isLoading}
            style={styles.redeemButton}
          >
            {isLoading ? 'Redeeming...' : 'Redeem'}
          </button>
        </div>
      </div>
    </>
  );
};

const styles = {
  closeButton: {
    marginTop: 22,
    marginRight: 24,
    cursor: 'pointer',
  },
  title: {
    color: '#EFEFEF',
    fontFamily: 'General Sans',
    fontSize: 20,
    fontWeight: 500,
    margin: '0px',
    marginTop: '12px',
  },
  signUpText: {
    color: '#FFF',
    fontFamily: 'General Sans',
    fontSize: 13,
    fontWeight: 400,
    marginTop: '10px',
  },
  signUpTextLink: {
    color: '#FFF',
    fontFamily: 'General Sans',
    fontSize: 13,
    fontWeight: 600,
    textDecoration: 'underline',
  },
  modalContainer: {
    borderRadius: 20,
    border: '1px solid #828282',
    backgroundColor: '#1A1A1A',
    borderColor: '#828282',
    width: 500,
    height: 335,
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
  },
  inputContainer: {
    width: '50%',
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    marginTop: 40,
  },
  input: {
    width: '250px',
    height: 40,
    backgroundColor: '#2A2A2A',
    border: '1px solid #828282',
    borderRadius: 8,
    padding: '0 15px',
    color: '#FFFFFF',
    fontSize: 14,
    outline: 'none',
    margin:0
  },
  redeemButton: {
    width: '280px',
    height: 40,
    backgroundColor: '#007AFF',
    border: 'none',
    borderRadius: 8,
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 500,
    cursor: 'pointer',
    margin:0,
    marginTop: 20,
    ':disabled': {
      opacity: 0.7,
      cursor: 'not-allowed',
    },
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 12,
    marginTop: 8,
    marginBottom: -12,
    alignSelf: 'flex-start',
  },
} as const;
export default RedeemVoucherModal;
