import React from 'react';
import {Route, Routes} from 'react-router';
import {PersistentAIChatProvider} from '../../../components/codeEditor/PersistentAIChat';
import {themeColors} from '../../../components/codeEditor/darkTheme';
import {BuildManagerRouter} from '../../buildManager';
import {PublishFlowRouter} from '../../publishFlowV1/index';
import EditorContainer from '../../editor/containers/EditorContainer';
import TileEditorContainer from '../editor/containers/TileEditorContainer';
import TileCodeEditorContainer from '../editor/containers/TileCodeEditorContainer';
import {IntegrationRouter} from '../integrations';
import AnalyticsPage from '../analytics/AnalyticsPage';
import NotificationsPage from '../notifications/NotificationsPage';
import {APPTILE_TILE_ENV} from '../../../../.env.json';

const DashboardWithPersistentChat: React.FC = () => {
  return (
    <PersistentAIChatProvider themeColors={themeColors}>
      <Routes>
        <Route path="studio" element={<EditorContainer />} />
        <Route path="builds/*" element={<BuildManagerRouter />} />
        <Route
          path="publish/dashboard/*"
          element={<PublishFlowRouter />}
        />
        <Route
          path="dashboard/codeEditor/:pluginName"
          element={<TileCodeEditorContainer />}
        />
        <Route
          path="dashboard/*"
          element={APPTILE_TILE_ENV == 'local' ? <EditorContainer /> : <TileEditorContainer />}
        />
        <Route
          path="dashboard/integrations/*"
          element={<IntegrationRouter />}
        />
        <Route
          path="dashboard/analytics/*"
          element={<AnalyticsPage />}
        />
        <Route
          path="dashboard/notifications/*"
          element={<NotificationsPage />}
        />
      </Routes>
    </PersistentAIChatProvider>
  );
};

export default DashboardWithPersistentChat;
