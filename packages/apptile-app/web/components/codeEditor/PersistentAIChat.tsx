import React, {createContext, useContext, useRef, useState, useCallback, useEffect} from 'react';
import {useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import {AIClient} from './aiClient';
import {ThemeColors} from './themetype';
import {View, StyleSheet} from 'react-native';
import {useParams} from 'react-router';

// Context for managing persistent AI chat
interface PersistentAIChatContextType {
  showChat: boolean;
  setShowChat: (show: boolean) => void;
  chatRef: React.RefObject<any>;
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
}

const PersistentAIChatContext = createContext<PersistentAIChatContextType | null>(null);

export const usePersistentAIChat = () => {
  const context = useContext(PersistentAIChatContext);
  if (!context) {
    throw new Error('usePersistentAIChat must be used within a PersistentAIChatProvider');
  }
  return context;
};

interface PersistentAIChatProviderProps {
  children: React.ReactNode;
  themeColors: ThemeColors;
}

export const PersistentAIChatProvider: React.FC<PersistentAIChatProviderProps> = ({
  children,
  themeColors,
}) => {
  const [showChat, setShowChat] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const chatRef = useRef<any>(null);
  const params = useParams();
  const appId = params?.id;

  const contextValue = {
    showChat,
    setShowChat,
    chatRef,
    isVisible,
    setIsVisible,
  };

  return (
    <PersistentAIChatContext.Provider value={contextValue}>
      <View style={styles.container}>
        {children}
        {showChat && appId && (
          <View style={[styles.chatOverlay, !isVisible && styles.hidden]}>
            <AIClient
              ref={chatRef}
              appId={appId}
              themeColors={themeColors}
              historyApi={`${window.PLUGIN_SERVER_URL}/plugins/chathistory/v2/${appId}`}
              completionApi={`${window.PLUGIN_SERVER_URL}/home/<USER>/prompt`}
              editorView={{current: null}}
              chatWidthInPercentage="100%"
              agentName="Architect"
              usePlanner={true}
              onArchitectClick={() => {}}
              onCompletionRunDone={(err) => {
                if (err) {
                  console.error('[PERSISTENT_CHAT] Chat completion error:', err);
                }
              }}
            />
          </View>
        )}
      </View>
    </PersistentAIChatContext.Provider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  chatOverlay: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    width: '40%',
    zIndex: 1000,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderLeftWidth: 1,
    borderLeftColor: '#333',
  },
  hidden: {
    display: 'none',
  },
});
