import React, {useEffect, useRef, useState, RefObject, useCallback, forwardRef, useImperativeHandle} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {store} from "apptile-core";
import {EditorView} from 'codemirror';
import {ThemeColors} from './themetype';
import {runChatCompletionRequest, SET_CHAT_RUNNING} from '../../actions/aiActions';
import TokenApis from '../../api/TokenApi';
import {useParams} from 'react-router';
import {DEFAULT_MODEL, DEFAULT_PROVIDER} from './aiModels';
import {View, StyleSheet, Text, Pressable} from 'react-native';
import { CirclePlus, SendButton } from '../../views/prompt-to-app/editor/components/svgelems';
import {
  ChatHistory, 
  ChatThread,
  LiveMessageHandle,
  ProcessingText,
} from "./chatBubbles"
import theme from '../../views/prompt-to-app/styles-prompt-to-app/theme';
import {setLandingPagePrompt} from '../../actions/editorActions';
import useMountEffect from '../../common/hooks/useMountEffect';

// Helper function to fetch chat history
export async function fetchChatMessages(
  historyApi: string,
  model: string,
  provider: 'openai' | 'claude' | 'google' | 'amazon',
  setChatHistory: any,
  messageContainer: any,
  onPlannerLoaded?: () => void,
): Promise<ChatHistory> {
  provider = provider || 'openai';
  const response = await fetch(`${historyApi}/provider/${provider}/model/${model}`);
  if (!response.ok || !response.body) {
    throw new Error(`Failed to fetch chat history: ${response.statusText}`);
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let done;
  let fullResponse = '';
  let processedUpto = 0;
  let history: ChatHistory = {};
  do {
    const readResult = await reader.read();
    done = readResult.done;
    const chunk = decoder.decode(readResult.value, {stream: true});
    fullResponse += chunk;
    const boundaryMarker = 'plugin_response_json_separator';
    let boundary = fullResponse.indexOf(boundaryMarker, processedUpto);
    while (boundary > processedUpto) {
      const parsedResponse = JSON.parse(fullResponse.substring(processedUpto, boundary));
      processedUpto = boundary + boundaryMarker.length;
      if (parsedResponse.pluginName) {
        parsedResponse.messages.forEach(message => (message.showing_details = false));
        setChatHistory(prev => {
          return {
            ...prev,
            [parsedResponse.pluginName]: {
              chat: parsedResponse.chat,
              messages: parsedResponse.messages,
            },
          };
        });
      } else {
        setChatHistory(prev => {
          return {
            ...prev,
            planner: {
              chat: parsedResponse.chat,
              messages: parsedResponse.messages,
            },
          };
        });
        setTimeout(() => {
          if (messageContainer.current) {
            messageContainer.current.scrollTop = messageContainer.current.scrollHeight;
          }
          if (onPlannerLoaded) {
            onPlannerLoaded();
          }
        }, 100);
      }

      boundary = fullResponse.indexOf(boundaryMarker, processedUpto);
    }
  } while (!done);
  // Validate messages
  // history.messages = history.messages.reverse();
  return history;
}

type AIClientProps = {
  themeColors: ThemeColors;
  historyApi: string;
  completionApi: string;
  editorView: RefObject<EditorView | null>;
  onCompletionRunDone: (error?: any) => void;
  appId: string;
  usePlanner: boolean;
  chatWidthInPercentage: string;
  agentName: string;
  onArchitectClick: () => void;
};

export const AIClient = forwardRef((props: AIClientProps, ref) => {
  const user = useSelector(state => state.user);
  const usersLLM = user?.user?.llmModel ?? "";
  let model = DEFAULT_MODEL;
  let provider = DEFAULT_PROVIDER;
  if (usersLLM) {
    [provider, model] = usersLLM.split(",");
  }
  logger.info("Using llm: ", model, provider);
  debugger
  const [hoveredItem, setHoveredItem] = useState("");
  const [chatContinuationStatus, setChatContinuationStatus] = useState({status: 'notchecked', prompt: ''});
  const [chatHistory, setChatHistory] = useState<ChatHistory | null>(null);
  const [tokenCount, setTokenCount] = useState({
    used: 0,
    max: 0,
  });
  const [showTokenLimitModal, setShowTokenLimitModal] = useState(false);
  const messageContainer = useRef<HTMLDivElement>(null);
  const prompterInputRef = useRef(null);
  const liveMessage = useRef<LiveMessageHandle>(null);
  const tokenIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [showProcessing, setShowProcessing] = useState(false);
  const dispatch = useDispatch();  

  useEffect(() => {
    if (chatContinuationStatus.status !== 'checked') {
      const initialPrompt = sessionStorage.getItem('initialChatPrompt');
      if (initialPrompt) {
        setChatContinuationStatus({
          status: 'checked',
          prompt: initialPrompt,
        });
      } else {
        setChatContinuationStatus({
          status: 'checked',
          prompt: '',
        });
      }
    }

    console.log('Chat continuation status: ', chatContinuationStatus);
  }, [chatContinuationStatus]);

  const updateChatHistory = useCallback(
    (model: string, provider: 'claude' | 'openai' | 'google' | 'amazon', onPlannerLoaded?: (() => void)) => {
      return fetchChatMessages(props.historyApi, model, provider, setChatHistory, messageContainer, onPlannerLoaded)
        .catch(err => {
          console.error('Failed to get chat history: ', err);
        });
    },
    [props.historyApi, setChatHistory],
  );

  // Get AI state from Redux
  // const aiState = useSelector((state: EditorRootState) => state.ai);

  // Function to check token usage and update UI
  //Get org id from the url
  const orgId = useParams().orgId;

  const checkTokenUsage = useCallback(async () => {
    if (!orgId) return;
    try {
      // This would be the actual API call in production
      const response = await TokenApis.getTokenStats(orgId as string);

      const result = {
        used: response.data.totalUsed,
        max: response.data.totalAllocated,
      };
      // Update token count state
      setTokenCount(result);

      // Show modal if tokens are exhausted
      if (response.data.totalUsed >= response.data.totalAllocated) {
        setShowTokenLimitModal(true);
      }
      return result;
    } catch (error) {
      console.error('Failed to fetch token stats:', error);
    }
  }, [orgId]);

  useMountEffect(() => {
    checkTokenUsage();
  });

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (tokenIntervalRef.current) {
        clearInterval(tokenIntervalRef.current);
        tokenIntervalRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    return () => {
      alert(5)
    };
  }, []);

  // useEffect(() => {
  //   console.log("Livemessage: ", liveMessage)
  //   window.liveMessage = liveMessage;
  // }, [liveMessage.current]);

  useEffect(() => {
    updateChatHistory(model, provider, () => {
      if (prompterInputRef.current) {
        let initialPrompt = store.getState().editor.get('promptFromLandingPage');
        //Check if token expired
        // if (isTokenLimitExpired) {
        //   setShowTokenLimitModal(true);
        //   return;
        // }
        checkTokenUsage()
        .then((tokenCount) => {
          console.log("Checked token count: ", tokenCount);
          // const isTokenLimitExpired = tokenCount && tokenCount.max >= 0 && tokenCount.used >= tokenCount.max;
          const div = prompterInputRef.current;
          if (initialPrompt) { // && div && !isTokenLimitExpired) {
            div.innerHTML = initialPrompt;
            onSendPrompt(initialPrompt);
            dispatch(setLandingPagePrompt(''));
          }
        })
        .catch(err => {
          console.error("Failed to check token usage!", err);
        }) 
      }
    });
  }, [checkTokenUsage, updateChatHistory]);;

  const onSendPrompt = useCallback(
    (text: string) => {
      if (text) {
        text = text.replace(/<div><\/div>/g, '');
        text = text.replace(/<br>/g, '\n');
      } else {
        return;
      }
      if (prompterInputRef.current) {
        prompterInputRef.current.contentEditable = false;
        prompterInputRef.current.innerHTML = "";
        if (liveMessage.current && liveMessage.current.planner) {
          liveMessage.current.planner.setMessageText("");
          liveMessage.current.planner.setToolText([]);
          liveMessage.current.planner.showMsgDiv();
          setShowProcessing(true);
        }

        if (messageContainer.current) {
          messageContainer.current.scrollTop = messageContainer.current.scrollHeight;
        }
      }
      
      dispatch({
        type: SET_CHAT_RUNNING,
        payload: true,
      });

      const updateLiveMessageContent = (
        contentHtml: string, 
        toolCall: Array<{id: string; tool: string; partialInput: string;}>,
        extra: {isMessageEnd: boolean; isToolEnd: boolean; forPlanner: boolean;}
      ) => {
        if (liveMessage.current) {
          if ((extra.forPlanner && liveMessage.current.planner) || (!extra.forPlanner && !liveMessage.current.plugin)) {
            liveMessage.current.planner.showMsgDiv();
            liveMessage.current.planner.setMessageText(contentHtml);
            liveMessage.current.planner.setToolText(toolCall);
          } else if (liveMessage.current.plugin) {
            liveMessage.current.planner.hide();
            liveMessage.current.plugin.showMsgDiv();
            liveMessage.current.plugin.setMessageText(contentHtml);
            liveMessage.current.plugin.setToolText(toolCall);
            const agentChats = document.querySelectorAll(".thin-scrollbar.expanded-agent-chat");
            agentChats.forEach(el => {
              el.scrollTop = el.scrollHeight;
            })
          }
          setShowProcessing(true);
        }  

        if (messageContainer.current) {
          messageContainer.current.scrollTop = messageContainer.current.scrollHeight;
        }

        if (extra.isMessageEnd || extra.isToolEnd) {
          updateChatHistory(
            model, 
            provider,
          )
        }
      }; 

      // Dispatch the action to run chat completion
      dispatch(
        runChatCompletionRequest({
          prompt: text,
          model: model,
          provider: provider,
          completionUrl: props.completionApi,
          appId: props.appId,
          usePlanner: props.usePlanner,
          liveMessageSubscriber: updateLiveMessageContent,
          onCompleted: err => {
            if (prompterInputRef.current) {
              prompterInputRef.current.contentEditable = true;
              prompterInputRef.current.innerHTML = "";
              dispatch({
                type: SET_CHAT_RUNNING,
                payload: false,
              });
            }

            if (liveMessage.current) { 
              if (liveMessage.current.planner) {
                liveMessage.current.planner.hide();
              }

              if (liveMessage.current.plugin) {
                liveMessage.current.plugin.hide();
              }
              setShowProcessing(false)
            }

            console.log('[AGENT] soft reloading');
            // dispatch(softRestartConfig());
            props.onCompletionRunDone(err);
            updateChatHistory(
              model, 
              provider
            );
            
            // Check token usage after completion
            checkTokenUsage();
          },
        }),
      );
    },
    [
      props.completionApi,
      props.appId,
      props.usePlanner,
      props.onCompletionRunDone,
      liveMessage,
      checkTokenUsage,
      updateChatHistory,
      dispatch,
    ],
  );

  useImperativeHandle(ref, () => {
    return {
      sendContextfulChat: (text: string) => {
        if (prompterInputRef.current) {
          prompterInputRef.current.innerHTML = text;
          onSendPrompt(text);
        } else {
          console.error("Uh oh! This shouldn't have happened!")
        }
      },
      resetChat: () => {
        updateChatHistory(model, provider);
        if (liveMessage.current.planner) {
          liveMessage.current.planner.hide();
        }

        if (liveMessage.current.plugin) {
          liveMessage.current.plugin.hide();
        }
      }
    };
  }, [prompterInputRef, liveMessage])

  const handleKeyDown = useCallback((ev) => {
    if (ev.shiftKey && ev.key === "Enter" && prompterInputRef.current) {
      const newLine = document.createElement("div");
      prompterInputRef.current.appendChild(newLine);
      const range = document.createRange();
      range.selectNodeContents(newLine);
      range.collapse(false);

      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } else if (ev.key === "Enter" && prompterInputRef.current) {
      ev.preventDefault();
      onSendPrompt(prompterInputRef.current.innerHTML);
    }
  }, [prompterInputRef, onSendPrompt])

  const handleFilePick = useCallback((ev: React.ChangeEvent<HTMLInputElement>) => {
    const file = ev.target.files?.[0];
    if (file) {
      const fileReader = new FileReader();
      fileReader.onload = e => {
        const dataUrl = e.target?.result?.toString();
        if (dataUrl && prompterInputRef.current) {
          const existingContent = prompterInputRef.current.innerHTML;
          prompterInputRef.current.innerHTML = existingContent + `<img src="${dataUrl}">`;
        } else {
          console.error("Didn't get any data from file");
        }
        ev.target.value = "";
      };

      fileReader.onerror = err => {
        console.error('Failed to read file', err);
        ev.target.value = "";
      };

      fileReader.readAsDataURL(file);
    } else {
      console.log('Ignoring event: ', ev);
    }
  }, [prompterInputRef]);

  let progressWidth = (tokenCount.used / Math.max(tokenCount.max, 1) * 100);
  if ((tokenCount.max === 0) || (tokenCount.used === 0)) {
    progressWidth = 0;
  }

  if (progressWidth > 100) {
    progressWidth = 100;
  }


  return (
    <View nativeID="aiclient-root" style={styles.root}>
      <View style={styles.chatBoxPadder}>
        <View nativeID="chat-message-container" ref={messageContainer} style={styles.chatMessageContainer}>
          <ChatThread
            ref={liveMessage}
            messages={chatHistory?.planner?.messages || []}
            chatHistory={chatHistory}
            setChatHistory={setChatHistory}
            showPlannerHeader={false}
            refkey="planner"
          />
        </View>
        {showProcessing && <ProcessingText />}
      </View>
      <View style={styles.promptInput}>
        <div
          id="prompter-input"
          ref={prompterInputRef}
          onKeyDown={handleKeyDown}
          contentEditable={true}
          className={'editor-prompt-input body-text'}
          data-placeholder="Ask Tile..."
          style={{
            flex: 1,
            color: 'white',
            border: 'none',
            outline: 'none',
            wordBreak: 'break-all',
            overflowX: 'auto',
            maxHeight: 249,
            scrollbarColor: 'transparent transparent',
          }}></div>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-end',
          }}>
          <View
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative'
            }}
          >
            <CirclePlus isHovered={hoveredItem === "prompterUploadButton"}/>
            <input
              type="file"
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                width: '100%',
                height: '100%',
                opacity: 0,
                cursor: 'pointer',
              }}
              onMouseEnter={() => {
                setHoveredItem("prompterUploadButton");
              }}
              onMouseLeave={() => {
                setHoveredItem("");
              }}
              onChange={handleFilePick}
              accept="image/png,image/jpeg"
            />
          </View>
          <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
            <View style={{flexDirection: 'column', paddingRight: theme.THIN_GAP * 2, alignItems: 'flex-end'}}>
              <View style={{flexDirection: 'row', paddingBottom: theme.THIN_GAP}}>
                <Text style={{color: theme.ACCENT}}>{Math.floor(tokenCount.used / 10000).toFixed(0)}</Text>
                <Text style={{color: theme.FOREGROUND_OP_HALF}}>/{Math.floor(tokenCount.max / 10000).toFixed(0)}</Text>
                <Text style={{color: theme.FOREGROUND, marginLeft: theme.THIN_GAP}}>credits</Text>
              </View>
              <View
                style={{
                  width: 130,
                  height: 4,
                  backgroundColor: theme.FOREGROUND_OP_HALF,
                  borderRadius: 2,
                }}>
                <View
                  style={{
                    height: '100%',
                    width: progressWidth,
                    backgroundColor: theme.FOREGROUND,
                    borderRadius: 2,
                  }}
                />
              </View>
            </View>
            <Pressable 
              onHoverIn={() => {
                setHoveredItem("prompterInputSendButton")
              }}
              onHoverOut={() => {
                setHoveredItem("");
              }}
              onPress={() => {
                if (prompterInputRef.current) {
                  const message = prompterInputRef.current.innerHTML;
                  onSendPrompt(message);
                }
              }
            }>
              <SendButton isHovered={hoveredItem === "prompterInputSendButton"} />
            </Pressable>
          </View>
        </View>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  root: {
    flexDirection: "column",
    display: "flex",
    flex: 1,
    backgroundColor: 'red'
  },
  chatBoxPadder: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: theme.BACKGROUND,
    marginBottom: theme.THIN_GAP,
    borderRadius: theme.CORNER_RADIUS,
    padding: 20
  },
  chatMessageContainer: {
    overflowY: 'auto',
    overflowX: 'hidden',
    paddingVertical: 10,
    flexDirection: 'column',
    flex: 1,
    // marginRight: -12
  },
  promptInput: {
    backgroundColor: theme.BACKGROUND,
    borderRadius: theme.CORNER_RADIUS,
    minHeight: 136,
    padding: theme.THIN_GAP * 2,
  }
});